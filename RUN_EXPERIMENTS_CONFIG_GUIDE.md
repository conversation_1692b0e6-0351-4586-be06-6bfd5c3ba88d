# run_experiments.sh 配置指南

本文档详细说明了重构后的run_experiments.sh脚本的配置结构和参数说明。

## 🎯 重构概述

### 主要改进
- ✅ **配置集中化**：所有配置参数移至脚本开头
- ✅ **内存优化**：从31GB堆内存升级到80GB
- ✅ **结构清晰**：配置区域与功能区域分离
- ✅ **易于维护**：统一的变量命名和注释

### 脚本结构
```
run_experiments.sh
├── 配置区域 (第1-65行)
│   ├── 实验基础配置
│   ├── Java应用程序配置
│   ├── 实验参数配置
│   ├── 内存配置
│   ├── PLI缓存配置
│   ├── G1GC详细配置
│   ├── 性能分析配置
│   └── 日志和调试配置
└── 脚本功能区域 (第66行以后)
    ├── 工具函数
    ├── 系统检查
    ├── JVM参数构建
    ├── 实验执行
    └── 结果处理
```

## 📋 配置参数详解

### 1. 实验基础配置
```bash
DATASET_PATH='data/int/EQ-500K-12.csv'     # 数据集路径
RESULTS_FILE='result/result0807_test.csv'  # 结果文件路径
PLI_MODE='original'                        # PLI模式: original 或 dynamic
```

**说明**：
- `DATASET_PATH`：可以是单个文件或目录路径
- `RESULTS_FILE`：实验结果CSV文件的保存路径
- `PLI_MODE`：控制PLI算法实现方式

### 2. Java应用程序配置
```bash
JAR_FILE="AFD-algorithms/experiment/target/experiment-1.0-SNAPSHOT-jar-with-dependencies.jar"
MAIN_CLASS="experiment.ExperimentRunner"
```

**说明**：
- `JAR_FILE`：编译后的实验JAR文件路径
- `MAIN_CLASS`：Java主类名称

### 3. 实验参数配置
```bash
SAMPLING_MODE="ALL"                        # 采样模式
RUN_MODE="APPEND"                          # 运行模式
RUN_TANE="true"                           # 是否运行TANE算法
ALGORITHM_TIMEOUT="120"                    # 算法超时时间（分钟）
MAX_ERROR="0.05"                          # 最大错误率
SAMPLE_PARAM="200"                        # 采样参数
RANDOM_SEED="114514"                      # 随机种子
```

**采样模式选项**：
- `ALL`：运行所有采样方法
- `NO_SAMPLING`：只运行不采样方法
- `RANDOM`：只运行随机采样
- `FOCUSED`：只运行聚焦采样
- `NEYMAN`：只运行Neyman采样

### 4. 内存配置 (80GB优化)
```bash
HEAP_SIZE="80g"                           # 80GB堆内存
NEW_SIZE="20g"                            # 20GB新生代（约25%）
METASPACE_SIZE="4g"                       # 4GB元空间
REGION_SIZE="64m"                         # 64MB G1区域大小
PAUSE_TARGET="200"                        # 200ms GC暂停目标
```

**内存配置说明**：
- **80GB堆内存**：充分利用128GB系统内存
- **20GB新生代**：保持25%的新生代比例
- **4GB元空间**：适合大型应用的类元数据
- **64MB区域大小**：适合80GB堆的G1GC配置
- **200ms暂停目标**：大堆适当放宽暂停时间

### 5. PLI缓存配置
```bash
PLI_CACHE_MB=32768                        # 32GB PLI缓存
PLI_CLEANUP_MB=26214                      # 26GB 清理阈值
```

**缓存配置说明**：
- **32GB PLI缓存**：利用堆外内存进行PLI缓存
- **26GB清理阈值**：80%的缓存使用率触发清理

### 6. G1GC详细配置
```bash
G1_NEW_SIZE_PERCENT=25                    # 新生代占比
G1_MAX_NEW_SIZE_PERCENT=35                # 最大新生代占比
G1_MIXED_GC_COUNT_TARGET=8                # 混合GC目标次数
G1_MIXED_GC_LIVE_THRESHOLD=85             # 混合GC存活阈值
G1_OLD_CSET_REGION_THRESHOLD=10           # 老年代收集集合区域阈值
G1_RESERVE_PERCENT=15                     # G1保留百分比
G1_HEAP_WASTE_PERCENT=5                   # 堆浪费百分比
```

### 7. 性能分析配置
```bash
ENABLE_PROFILING=true                     # 是否启用性能分析
PROFILING_DURATION=0                      # 跟随程序运行时间
PROFILING_INTERVAL=10ms                   # 采样间隔
ASYNC_PROFILER_PATH="/opt/async-profiler" # async-profiler安装路径
```

### 8. 日志和调试配置
```bash
GC_LOG_PREFIX="gc-128gb"                  # GC日志文件前缀
GC_LOG_FILE_COUNT=10                      # GC日志文件数量
GC_LOG_FILE_SIZE="100M"                   # GC日志文件大小
DEBUG_MODE="${DEBUG:-false}"              # 调试模式
```

## 🔧 配置修改指南

### 常见配置修改

#### 1. 修改数据集
```bash
# 单个文件
DATASET_PATH='data/your-dataset.csv'

# 目录（处理多个文件）
DATASET_PATH='data/your-directory'
```

#### 2. 调整内存配置
```bash
# 如果系统内存不足，可以减少堆内存
HEAP_SIZE="40g"                           # 40GB堆内存
NEW_SIZE="10g"                            # 相应调整新生代
PLI_CACHE_MB=16384                        # 相应调整PLI缓存
```

#### 3. 修改PLI模式
```bash
# 使用动态PLI实现
PLI_MODE='dynamic'
```

#### 4. 禁用性能分析
```bash
# 如果不需要性能分析
ENABLE_PROFILING=false
```

#### 5. 调整GC参数
```bash
# 如果需要更低的GC暂停时间
PAUSE_TARGET="100"                        # 100ms暂停目标

# 如果需要更大的G1区域
REGION_SIZE="128m"                        # 128MB区域大小
```

## 📊 内存配置对比

### 旧配置 vs 新配置

| 项目 | 旧配置 (31GB) | 新配置 (80GB) | 说明 |
|------|---------------|---------------|------|
| 堆内存 | 31GB | 80GB | 提升2.6倍 |
| 新生代 | 8GB | 20GB | 保持25%比例 |
| 元空间 | 2GB | 4GB | 适应大型应用 |
| G1区域 | 32MB | 64MB | 适合大堆 |
| GC暂停 | 100ms | 200ms | 大堆适当放宽 |
| PLI缓存 | 48GB | 32GB | 优化配比 |
| 压缩指针 | 启用 | 禁用 | 超过32GB限制 |

### 内存分配策略
```
总内存: 128GB
├── JVM堆内存: 80GB (62.5%)
├── PLI缓存: 32GB (25%)
├── 系统预留: 16GB (12.5%)
└── 其他应用: 可用空间
```

## 🚀 性能优化建议

### 1. 根据数据集大小调整
```bash
# 小数据集 (<1GB)
HEAP_SIZE="20g"
PLI_CACHE_MB=8192

# 中等数据集 (1-10GB)
HEAP_SIZE="40g"
PLI_CACHE_MB=16384

# 大数据集 (>10GB)
HEAP_SIZE="80g"
PLI_CACHE_MB=32768
```

### 2. 根据实验类型调整
```bash
# 快速测试
ALGORITHM_TIMEOUT="30"
SAMPLING_MODE="NO_SAMPLING"

# 完整实验
ALGORITHM_TIMEOUT="120"
SAMPLING_MODE="ALL"

# 性能基准测试
PLI_MODE='original'
ENABLE_PROFILING=true
```

### 3. 根据系统资源调整
```bash
# 高内存系统 (256GB+)
HEAP_SIZE="120g"
PLI_CACHE_MB=65536

# 标准内存系统 (128GB)
HEAP_SIZE="80g"
PLI_CACHE_MB=32768

# 低内存系统 (64GB)
HEAP_SIZE="40g"
PLI_CACHE_MB=16384
```

## ✅ 配置验证

### 验证配置合理性
```bash
# 检查内存配置
echo "堆内存: $HEAP_SIZE"
echo "PLI缓存: $(echo "scale=1; ${PLI_CACHE_MB}/1024" | bc)GB"
echo "总内存需求: $(echo "scale=1; ${HEAP_SIZE%g} + ${PLI_CACHE_MB}/1024" | bc)GB"

# 检查文件路径
ls -la "$DATASET_PATH"
ls -la "$JAR_FILE"

# 检查async-profiler
ls -la "$ASYNC_PROFILER_PATH/bin/asprof"
```

### 配置测试
```bash
# 使用小数据集测试配置
DATASET_PATH='data/airport.csv'
ALGORITHM_TIMEOUT="5"
./run_experiments.sh
```

## 🔍 故障排除

### 常见配置问题

1. **内存不足**
   - 减少`HEAP_SIZE`和`PLI_CACHE_MB`
   - 检查系统可用内存

2. **文件路径错误**
   - 验证`DATASET_PATH`和`JAR_FILE`路径
   - 检查文件权限

3. **GC性能问题**
   - 调整`PAUSE_TARGET`
   - 修改`REGION_SIZE`

4. **PLI缓存问题**
   - 减少`PLI_CACHE_MB`
   - 调整`PLI_CLEANUP_MB`

## 📚 参考资源

- [G1GC调优指南](https://docs.oracle.com/en/java/javase/11/gctuning/garbage-first-garbage-collector.html)
- [JVM内存管理最佳实践](https://docs.oracle.com/en/java/javase/11/gctuning/introduction-garbage-collection-tuning.html)
- [Async Profiler使用指南](https://github.com/jvm-profiling-tools/async-profiler)

通过这个配置指南，您可以根据具体需求灵活调整run_experiments.sh脚本的各项参数，以获得最佳的实验性能。

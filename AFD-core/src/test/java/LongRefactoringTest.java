import measure.SimpleMeasure;
import model.DataSet;
import pli.PLICache;
import utils.LongBitSetUtils;
import utils.BitSetUtils;

import java.util.BitSet;
import java.util.Arrays;
import java.util.List;

/**
 * 测试BitSet到long重构的正确性
 * 验证新的long版本方法与原有BitSet版本产生相同的结果
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/4
 */
public class LongRefactoringTest {
    
    public static void main(String[] args) {
        System.out.println("开始BitSet到long重构测试...");
        
        try {
            testLongBitSetUtils();
            testSimpleMeasure();
            System.out.println("✅ 所有测试通过！重构成功。");
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试LongBitSetUtils的新增方法
     */
    private static void testLongBitSetUtils() {
        System.out.println("测试LongBitSetUtils...");
        
        // 测试基本位操作
        long bits = LongBitSetUtils.createFromBits(0, 2, 5, 7);
        assert LongBitSetUtils.cardinality(bits) == 4 : "cardinality测试失败";
        assert LongBitSetUtils.testBit(bits, 0) : "testBit测试失败";
        assert LongBitSetUtils.testBit(bits, 2) : "testBit测试失败";
        assert !LongBitSetUtils.testBit(bits, 1) : "testBit测试失败";
        
        // 测试BitSet转换
        BitSet bitSet = new BitSet();
        bitSet.set(0);
        bitSet.set(2);
        bitSet.set(5);
        bitSet.set(7);
        
        long convertedLong = LongBitSetUtils.bitSetToLong(bitSet, 10);
        BitSet convertedBitSet = LongBitSetUtils.longToBitSet(bits, 10);
        
        assert bits == convertedLong : "BitSet到long转换测试失败";
        assert bitSet.equals(convertedBitSet) : "long到BitSet转换测试失败";
        
        // 测试列表转换
        List<Integer> list = Arrays.asList(0, 2, 5, 7);
        List<Integer> convertedList = LongBitSetUtils.longToList(bits);
        assert list.equals(convertedList) : "long到List转换测试失败";
        
        long convertedFromList = LongBitSetUtils.listToLong(list);
        assert bits == convertedFromList : "List到long转换测试失败";
        
        // 测试边界检查
        try {
            LongBitSetUtils.validateColumnCount(65);
            assert false : "应该抛出异常";
        } catch (IllegalArgumentException e) {
            // 预期的异常
        }
        
        try {
            LongBitSetUtils.validateBitIndex(64);
            assert false : "应该抛出异常";
        } catch (IllegalArgumentException e) {
            // 预期的异常
        }
        
        System.out.println("✅ LongBitSetUtils测试通过");
    }
    
    /**
     * 测试SimpleMeasure的long版本与BitSet版本结果一致性
     */
    private static void testSimpleMeasure() {
        System.out.println("测试SimpleMeasure...");
        
        // 创建模拟数据集
        DataSet mockDataSet = createMockDataSet();
        PLICache mockCache = new PLICache(mockDataSet);
        SimpleMeasure measure = new SimpleMeasure();
        
        // 测试不同的位集合
        int[] testCases = {0, 1, 3, 7, 15, 31}; // 不同cardinality的测试用例
        
        for (int testBits : testCases) {
            // 创建BitSet版本
            BitSet bitSet = new BitSet();
            for (int i = 0; i < 6; i++) {
                if ((testBits & (1 << i)) != 0) {
                    bitSet.set(i);
                }
            }
            
            // 转换为long版本
            long longBits = LongBitSetUtils.bitSetToLong(bitSet, mockDataSet.getColumnCount());
            
            // 比较calculateError结果
            double bitSetResult = measure.calculateError(bitSet, 0, mockDataSet, mockCache);
            double longResult = measure.calculateError(longBits, 0, mockDataSet, mockCache);
            
            assert Math.abs(bitSetResult - longResult) < 1e-10 : 
                String.format("calculateError结果不一致: BitSet=%.10f, long=%.10f", bitSetResult, longResult);
            
            // 比较estimateError结果
            double bitSetEstimate = measure.estimateError(bitSet, 0, mockDataSet, mockCache, null);
            double longEstimate = measure.estimateError(longBits, 0, mockDataSet, mockCache, null);
            
            assert Math.abs(bitSetEstimate - longEstimate) < 1e-10 : 
                String.format("estimateError结果不一致: BitSet=%.10f, long=%.10f", bitSetEstimate, longEstimate);
        }
        
        System.out.println("✅ SimpleMeasure测试通过");
    }
    
    /**
     * 创建模拟数据集用于测试
     */
    private static DataSet createMockDataSet() {
        // 创建一个简单的6列数据集
        return new DataSet() {
            @Override
            public int getColumnCount() {
                return 6;
            }
            
            @Override
            public int getRowCount() {
                return 100;
            }
            
            @Override
            public List<String> getRow(int rowIndex) {
                return Arrays.asList("val1", "val2", "val3", "val4", "val5", "val6");
            }
            
            @Override
            public String getColumnName(int columnIndex) {
                return "col" + columnIndex;
            }
        };
    }
}

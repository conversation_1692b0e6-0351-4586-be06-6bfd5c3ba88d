import measure.*;
import sampling.*;
import utils.LongBitSetUtils;
import utils.MinFDTrie;
import utils.MaxFDTrie;
import utils.HittingSet;
import utils.BitSetUtils;

import java.util.*;

/**
 * 最终重构验证测试
 * 验证BitSet到Long重构的完整性和正确性
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/4
 */
public class FinalRefactoringValidationTest {
    
    public static void main(String[] args) {
        System.out.println("开始AFD算法BitSet到Long重构最终验证...");
        
        try {
            testLongBitSetUtils();
            testErrorMeasureLongVersions();
            testSamplingStrategyLongVersions();
            testTrieLongVersions();
            testHittingSetLongVersions();
            testPerformanceComparison();
            
            System.out.println("✅ 所有验证测试通过！重构成功完成。");
            
        } catch (Exception e) {
            System.err.println("❌ 验证测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试LongBitSetUtils的核心功能
     */
    private static void testLongBitSetUtils() {
        System.out.println("测试LongBitSetUtils核心功能...");
        
        // 测试基本位操作
        long bits = LongBitSetUtils.createFromBits(0, 2, 5, 7);
        assert LongBitSetUtils.cardinality(bits) == 4 : "cardinality测试失败";
        assert LongBitSetUtils.testBit(bits, 0) : "testBit测试失败";
        assert !LongBitSetUtils.testBit(bits, 1) : "testBit测试失败";
        
        // 测试集合操作
        long bits2 = LongBitSetUtils.createFromBits(1, 2, 3);
        long union = LongBitSetUtils.union(bits, bits2);
        assert LongBitSetUtils.cardinality(union) == 6 : "union测试失败";
        
        // 测试转换功能
        List<Integer> list = LongBitSetUtils.longToList(bits);
        long convertedBack = LongBitSetUtils.listToLong(list);
        assert bits == convertedBack : "转换测试失败";
        
        System.out.println("✅ LongBitSetUtils测试通过");
    }
    
    /**
     * 测试ErrorMeasure的long版本方法
     */
    private static void testErrorMeasureLongVersions() {
        System.out.println("测试ErrorMeasure long版本方法...");
        
        // 创建模拟数据集
        MockDataSet dataSet = new MockDataSet(6, 100);
        MockPLICache cache = new MockPLICache();
        
        // 测试SimpleMeasure
        SimpleMeasure simpleMeasure = new SimpleMeasure();
        long lhs = LongBitSetUtils.createFromBits(0, 1, 2);
        double error = simpleMeasure.calculateError(lhs, 3, dataSet, cache);
        assert error >= 0 && error <= 1 : "SimpleMeasure错误率超出范围";
        
        // 测试G1Measure
        G1Measure g1Measure = new G1Measure();
        double g1Error = g1Measure.calculateError(lhs, 3, dataSet, cache);
        assert g1Error >= 0 && g1Error <= 1 : "G1Measure错误率超出范围";
        
        System.out.println("✅ ErrorMeasure long版本测试通过");
    }
    
    /**
     * 测试SamplingStrategy的long版本方法
     */
    private static void testSamplingStrategyLongVersions() {
        System.out.println("测试SamplingStrategy long版本方法...");
        
        MockDataSet dataSet = new MockDataSet(6, 100);
        MockPLICache cache = new MockPLICache();
        
        // 测试RandomSampling
        RandomSampling randomSampling = new RandomSampling(12345L);
        long lhs = LongBitSetUtils.createFromBits(0, 1);
        randomSampling.initialize(dataSet, cache, lhs, 2, 0.1);
        assert randomSampling.getSampleSize() > 0 : "RandomSampling样本大小错误";
        
        // 测试FocusedSampling
        FocusedSampling focusedSampling = new FocusedSampling(12345L);
        focusedSampling.initialize(dataSet, cache, lhs, 2, 0.1);
        // 注意：FocusedSampling可能返回空集，这是正常的
        
        System.out.println("✅ SamplingStrategy long版本测试通过");
    }
    
    /**
     * 测试Trie的long版本方法
     */
    private static void testTrieLongVersions() {
        System.out.println("测试Trie long版本方法...");
        
        // 测试MinFDTrie
        MinFDTrie minTrie = new MinFDTrie();
        long bits1 = LongBitSetUtils.createFromBits(0, 1);
        long bits2 = LongBitSetUtils.createFromBits(0, 1, 2);
        
        minTrie.add(bits1);
        assert minTrie.containsSubSetOf(bits2) : "MinFDTrie子集检查失败";
        
        // 测试MaxFDTrie
        MaxFDTrie maxTrie = new MaxFDTrie();
        maxTrie.add(bits2);
        assert maxTrie.containsSuperSetOf(bits1) : "MaxFDTrie超集检查失败";
        
        System.out.println("✅ Trie long版本测试通过");
    }
    
    /**
     * 测试HittingSet的long版本方法
     */
    private static void testHittingSetLongVersions() {
        System.out.println("测试HittingSet long版本方法...");
        
        HittingSet hittingSet = new HittingSet();
        long bits1 = LongBitSetUtils.createFromBits(0, 1);
        long bits2 = LongBitSetUtils.createFromBits(2, 3);
        
        hittingSet.add(bits1);
        hittingSet.add(bits2);
        
        List<Long> allSets = hittingSet.getAllMinimalHittingSetsLong();
        assert allSets.size() == 2 : "HittingSet大小错误";
        
        // 测试long版本的calculateHittingSet
        List<Long> inputSets = Arrays.asList(bits1, bits2);
        HittingSet calculated = BitSetUtils.calculateHittingSet(inputSets, 6);
        assert !calculated.isEmpty() : "calculateHittingSet结果为空";
        
        System.out.println("✅ HittingSet long版本测试通过");
    }
    
    /**
     * 测试性能对比
     */
    private static void testPerformanceComparison() {
        System.out.println("测试性能对比...");
        
        int iterations = 10000;
        
        // 测试long版本性能
        long startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            long bits = LongBitSetUtils.createFromBits(i % 6, (i + 1) % 6);
            LongBitSetUtils.cardinality(bits);
            LongBitSetUtils.longToList(bits);
        }
        long longTime = System.nanoTime() - startTime;
        
        // 测试BitSet版本性能
        startTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            BitSet bitSet = new BitSet();
            bitSet.set(i % 6);
            bitSet.set((i + 1) % 6);
            bitSet.cardinality();
            // 简单的转换操作
            List<Integer> list = new ArrayList<>();
            for (int j = bitSet.nextSetBit(0); j >= 0; j = bitSet.nextSetBit(j + 1)) {
                list.add(j);
            }
        }
        long bitSetTime = System.nanoTime() - startTime;
        
        double speedup = (double) bitSetTime / longTime;
        System.out.println("性能对比结果:");
        System.out.println("  Long版本时间: " + (longTime / 1_000_000) + "ms");
        System.out.println("  BitSet版本时间: " + (bitSetTime / 1_000_000) + "ms");
        System.out.println("  性能提升倍数: " + String.format("%.2f", speedup) + "x");
        
        assert speedup > 1.0 : "Long版本性能未达到预期";
        
        System.out.println("✅ 性能对比测试通过");
    }
    
    // 模拟类用于测试
    private static class MockDataSet implements model.DataSet {
        private final int columnCount;
        private final int rowCount;
        
        public MockDataSet(int columnCount, int rowCount) {
            this.columnCount = columnCount;
            this.rowCount = rowCount;
        }
        
        @Override
        public int getColumnCount() { return columnCount; }
        
        @Override
        public int getRowCount() { return rowCount; }
        
        @Override
        public List<String> getRow(int rowIndex) {
            List<String> row = new ArrayList<>();
            for (int i = 0; i < columnCount; i++) {
                row.add("value" + i + "_" + rowIndex);
            }
            return row;
        }
        
        @Override
        public String getColumnName(int columnIndex) {
            return "col" + columnIndex;
        }
    }
    
    private static class MockPLICache implements pli.PLICache {
        @Override
        public pli.PLI getOrCalculatePLI(BitSet targetColumns) {
            // 返回一个简单的模拟PLI
            return new MockPLI();
        }
        
        @Override
        public pli.PLI findBestCachedSubsetPli(BitSet lhs) {
            return new MockPLI();
        }
        
        @Override
        public pli.PLI getPLI(BitSet columns) {
            return new MockPLI();
        }
        
        @Override
        public boolean containsKey(BitSet columns) {
            return true;
        }
        
        @Override
        public pli.PLI get(List<Integer> key) {
            return new MockPLI();
        }
    }
    
    private static class MockPLI implements pli.PLI {
        @Override
        public List<Set<Integer>> getEquivalenceClasses() {
            // 返回简单的等价类
            List<Set<Integer>> classes = new ArrayList<>();
            classes.add(Set.of(0, 1, 2));
            classes.add(Set.of(3, 4));
            classes.add(Set.of(5));
            return classes;
        }
        
        @Override
        public int[] toAttributeVector() {
            return new int[]{1, 1, 1, 2, 2, 0}; // 简单的属性向量
        }
        
        @Override
        public int size() {
            return 3; // 3个等价类
        }
    }
}

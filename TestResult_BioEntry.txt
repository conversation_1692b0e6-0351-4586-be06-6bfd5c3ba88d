=== bio_entry.csv性能测试 ===
数据集信息: 184291行, 9列
2025-07-30 22:44:32 INFO  pli.OptimizedPLICache - 初始化完成，单列PLI数量: 9, 内存使用: 11MB
2025-07-30 22:44:32 INFO  utils.MemoryMonitor - 内存监控初始化完成，最大堆内存: 8116MB
2025-07-30 22:44:32 INFO  utils.MemoryMonitor - 内存监控已启动，间隔: 10000ms
2025-07-30 22:44:32 INFO  pli.PLIOptimizationIntegrator - 策略选择参数 - 行数: 184291, 列数: 9, 可用内存: 7406MB
2025-07-30 22:44:32 INFO  pli.PLIOptimizationIntegrator - PLI优化集成器初始化完成，数据集: 184291行×9列, 初始策略: 原始实现
2025-07-30 22:44:32 WARN  utils.MemoryMonitor - 内存监控已在运行中
2025-07-30 22:44:32 INFO  algorithm.Pyro - Pyro算法初始化完成，数据集: 184291行×9列
2025-07-30 22:44:32 INFO  algorithm.Pyro - Pyro算法开始执行，初始内存使用: 644MB
2025-07-30 22:44:32 INFO  algorithm.Pyro - 处理RHS属性: 0 (1/9)
2025-07-30 22:44:32 INFO  algorithm.SearchSpace - 开始搜索, rhs = 0
2025-07-30 22:44:32 INFO  algorithm.SearchSpace - root: Node{{}→0, error=1.7976931348623157E308}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - launchpad: Node{{1}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - 找到最小FD: Node{{1}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - launchpad: Node{{3}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - 找到最小FD: Node{{3}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 8}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 8}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 7, 8}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 8}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - 找到最小FD: Node{{4, 8}→0, error=0.003098377557111075}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 7, 8}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - 找到最小FD: Node{{4, 6}→0, error=0.003092951326713332}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5, 8}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 5, 8}→0, error=0.0}
2025-07-30 22:44:33 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 5, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 6, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 4, 5, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 4, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 5, 6, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 4, 6, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 6, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 5, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 5, 6, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 5, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5, 6, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 5, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 5, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 5, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 4, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5, 7}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5, 6}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 5}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 5}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 5}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 5, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 4, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 6, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 4, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 5, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 7}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 6}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 5}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 4}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 6, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 4, 5, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 6}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 4, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 7}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 6}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 5}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 5, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 6}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 5}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 4}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 5, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 7}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 7}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 5}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 6}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 7}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{4}→0, error=0.5553204189049867}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - 上升到节点: null
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - 添加最大nonFD: Node{{2, 4, 5, 7}→0, error=0.5553204189049867}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{6}→0, error=0.9499755819632102}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - 上升到节点: null
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - 添加最大nonFD: Node{{2, 5, 6, 7, 8}→0, error=0.9499755819632102}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{8}→0, error=0.9499864344240057}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{5}→0, error=1.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{7}→0, error=1.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{2}→0, error=1.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7, 8}→0, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - 搜索完成，内存统计 - 活跃节点: 256, 缓存节点: 0, 访问记录: 256
2025-07-30 22:44:34 INFO  algorithm.Pyro - 处理RHS属性: 1 (2/9)
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - 开始搜索, rhs = 1
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - root: Node{{}→1, error=1.7976931348623157E308}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{0}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - 找到最小FD: Node{{0}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - 找到最小FD: Node{{3}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 8}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 8}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 7, 8}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 8}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - 找到最小FD: Node{{4, 8}→1, error=0.003098377557111075}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 7, 8}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - 找到最小FD: Node{{4, 6}→1, error=0.003092951326713332}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5, 8}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 8}→1, error=0.0}
2025-07-30 22:44:34 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 6, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4, 5, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 6, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4, 6, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 6, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 5, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 6, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 5, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5, 6, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 5, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 5, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 5, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 4, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5, 7}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5, 6}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 5}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 5}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 5}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 5, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 4, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 6, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 4, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 5, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 7}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 6}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 5}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 4}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 6, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4, 5, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 6}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 7}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 6}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 5}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 5, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 6}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 5, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 7}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 7}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 5}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 6}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 7}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{4}→1, error=0.5553204189049867}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 上升到节点: null
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 添加最大nonFD: Node{{2, 4, 5, 7}→1, error=0.5553204189049867}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{6}→1, error=0.9499755819632102}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 上升到节点: null
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 添加最大nonFD: Node{{2, 5, 6, 7, 8}→1, error=0.9499755819632102}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{8}→1, error=0.9499864344240057}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{5}→1, error=1.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{7}→1, error=1.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{2}→1, error=1.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7, 8}→1, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 搜索完成，内存统计 - 活跃节点: 256, 缓存节点: 0, 访问记录: 256
2025-07-30 22:44:35 INFO  algorithm.Pyro - 处理RHS属性: 2 (3/9)
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 开始搜索, rhs = 2
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - root: Node{{}→2, error=1.7976931348623157E308}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 找到最小FD: Node{{0}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 找到最小FD: Node{{8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 找到最小FD: Node{{7}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 找到最小FD: Node{{6}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 找到最小FD: Node{{5}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 找到最小FD: Node{{4}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 找到最小FD: Node{{3}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 找到最小FD: Node{{1}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{4, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 5, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{5, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{4, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 6, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 5, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{6, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{5, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{4, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 7}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 6}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 5}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{7}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{6}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{5}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{4}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7, 8}→2, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 搜索完成，内存统计 - 活跃节点: 256, 缓存节点: 0, 访问记录: 256
2025-07-30 22:44:35 INFO  algorithm.Pyro - 处理RHS属性: 3 (4/9)
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 开始搜索, rhs = 3
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - root: Node{{}→3, error=1.7976931348623157E308}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 找到最小FD: Node{{0}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 找到最小FD: Node{{1}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 找到最小FD: Node{{4, 8}→3, error=0.003098377557111075}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 5}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 找到最小FD: Node{{4, 6}→3, error=0.003092951326713332}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 5, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 6, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 5, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5, 6, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 6, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 6, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 5, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5, 6, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 5, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 5, 6, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 5, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 5, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 5, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 4, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 5, 7}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 5, 6}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 5}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 5}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 5, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 4, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 6, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 4, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 7}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 6}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 5}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 4}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 6, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 5, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 6}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 7}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 6}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 5, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 6}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 7}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 7}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 5}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 6}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 7}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{4}→3, error=0.5553204189049867}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 上升到节点: null
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 添加最大nonFD: Node{{2, 4, 5, 7}→3, error=0.5553204189049867}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{6}→3, error=0.9499755819632102}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 上升到节点: null
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 添加最大nonFD: Node{{2, 5, 6, 7, 8}→3, error=0.9499755819632102}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{8}→3, error=0.9499864344240057}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{5}→3, error=1.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{7}→3, error=1.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{2}→3, error=1.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7, 8}→3, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 搜索完成，内存统计 - 活跃节点: 256, 缓存节点: 0, 访问记录: 256
2025-07-30 22:44:35 INFO  algorithm.Pyro - 处理RHS属性: 4 (5/9)
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 开始搜索, rhs = 4
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - root: Node{{}→4, error=1.7976931348623157E308}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0}→4, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 找到最小FD: Node{{0}→4, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1}→4, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 找到最小FD: Node{{1}→4, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{3}→4, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - 找到最小FD: Node{{3}→4, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1}→4, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2}→4, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 8}→4, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3}→4, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 7, 8}→4, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 8}→4, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 5}→4, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 7, 8}→4, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 5, 8}→4, error=0.0}
2025-07-30 22:44:35 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 6}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 6, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 5, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 5, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 6, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 6, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 6, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 6, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 5, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 5, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 6, 7}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 6}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 6}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 6}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 7}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 6}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 5}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 5}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 7}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 5, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 5}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 5, 6, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 5, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 5, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 5, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 5, 7}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 5, 6}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 5}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 5}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 6, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 5, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 7}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 6}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 5}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 6, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 7}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 6}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 6}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 7}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 6}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{3, 7}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 7}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{3, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{3, 6}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 5}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 6}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 7}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{6}→4, error=0.9488414998100819}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 上升到节点: null
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 添加最大nonFD: Node{{2, 5, 6, 7, 8}→4, error=0.9488414998100819}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{8}→4, error=0.9488523522708774}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{5}→4, error=0.9939009170329373}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2}→4, error=0.9939009170329373}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{7}→4, error=0.9939009170329373}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7, 8}→4, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 搜索完成，内存统计 - 活跃节点: 256, 缓存节点: 0, 访问记录: 256
2025-07-30 22:44:36 INFO  algorithm.Pyro - 已处理 5/9 个RHS属性，当前内存: 794MB，峰值: 1254MB
2025-07-30 22:44:36 INFO  algorithm.Pyro - 处理RHS属性: 5 (6/9)
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 开始搜索, rhs = 5
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - root: Node{{}→5, error=1.7976931348623157E308}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 找到最小FD: Node{{0}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 找到最小FD: Node{{8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 找到最小FD: Node{{7}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 找到最小FD: Node{{6}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 找到最小FD: Node{{4}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 找到最小FD: Node{{3}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 找到最小FD: Node{{2}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 找到最小FD: Node{{1}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{3, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{4, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{3, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 6, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{6, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{4, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{3, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 7}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 6}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{7}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{6}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{4}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{3}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7, 8}→5, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 搜索完成，内存统计 - 活跃节点: 256, 缓存节点: 0, 访问记录: 256
2025-07-30 22:44:36 INFO  algorithm.Pyro - 处理RHS属性: 6 (7/9)
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 开始搜索, rhs = 6
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - root: Node{{}→6, error=1.7976931348623157E308}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 找到最小FD: Node{{0}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 找到最小FD: Node{{8}→6, error=1.410819903413099E-4}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 找到最小FD: Node{{1}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{3}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 找到最小FD: Node{{3}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 4, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{3, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 5, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 4, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 7}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{3, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{3, 7}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 7}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{5, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{4, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{1, 5}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 5}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 7}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{4}→6, error=0.5524390905637854}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 上升到节点: null
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 添加最大nonFD: Node{{2, 4, 5, 7}→6, error=0.5524390905637854}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{2}→6, error=0.9333984480981062}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{5}→6, error=0.9333984480981062}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{7}→6, error=0.9333984480981062}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7, 8}→6, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 搜索完成，内存统计 - 活跃节点: 256, 缓存节点: 0, 访问记录: 256
2025-07-30 22:44:36 INFO  algorithm.Pyro - 处理RHS属性: 7 (8/9)
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 开始搜索, rhs = 7
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - root: Node{{}→7, error=1.7976931348623157E308}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0}→7, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 找到最小FD: Node{{0}→7, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{8}→7, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 找到最小FD: Node{{8}→7, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 8}→7, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{6, 8}→7, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 找到最小FD: Node{{6}→7, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{0, 6, 8}→7, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - launchpad: Node{{5, 6, 8}→7, error=0.0}
2025-07-30 22:44:36 INFO  algorithm.SearchSpace - 找到最小FD: Node{{5}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - 找到最小FD: Node{{4}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - 找到最小FD: Node{{3}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - 找到最小FD: Node{{2}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - 找到最小FD: Node{{1}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{2, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{2, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{4, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{3, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{2, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 5, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{5, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{4, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{3, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{2, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 6}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 5}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{6}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{5}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{4}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{3}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{2}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 8}→7, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - 搜索完成，内存统计 - 活跃节点: 256, 缓存节点: 0, 访问记录: 256
2025-07-30 22:44:37 INFO  algorithm.Pyro - 处理RHS属性: 8 (9/9)
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - 开始搜索, rhs = 8
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - root: Node{{}→8, error=1.7976931348623157E308}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - 找到最小FD: Node{{0}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - 找到最小FD: Node{{1}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{3}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - 找到最小FD: Node{{3}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{3, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - 找到最小FD: Node{{6}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 4, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 5, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 4, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 5, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 6}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 4, 5}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 4}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 5, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 6}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 5}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2, 3}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 6}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 5}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 4}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 2, 3}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3, 4}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 5}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 6}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 4}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 3}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{3, 6}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{3, 4}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{2, 3}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 3}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 6}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{3, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 2}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 6}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{3, 5}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 4}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 3}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{1, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{6}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 5}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{4}→8, error=0.5524336643333876}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - 上升到节点: null
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - 添加最大nonFD: Node{{2, 4, 5, 7}→8, error=0.5524336643333876}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{5}→8, error=0.9333984480981062}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{7}→8, error=0.9333984480981062}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{2}→8, error=0.9333984480981062}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - launchpad: Node{{0, 1, 2, 3, 4, 5, 6, 7}→8, error=0.0}
2025-07-30 22:44:37 INFO  algorithm.SearchSpace - 搜索完成，内存统计 - 活跃节点: 256, 缓存节点: 0, 访问记录: 256
2025-07-30 22:44:37 INFO  algorithm.Pyro - Pyro算法执行完成，耗时: 4464ms，发现FD数量: 47
2025-07-30 22:44:37 INFO  algorithm.Pyro - PLI性能统计: PLI性能统计 - 总请求: 2641, 原始: 2641(100.0%), 优化: 0(0.0%), 流式: 0(0.0%), 当前策略: 原始实现, 内存使用: 1710MB/21.1%
2025-07-30 22:44:37 INFO  algorithm.Pyro - 内存峰值: 1710MB
2025-07-30 22:44:37 INFO  pli.PLIOptimizationIntegrator - 关闭PLI优化集成器
2025-07-30 22:44:37 INFO  utils.MemoryMonitor - 内存监控已停止
2025-07-30 22:44:37 INFO  pli.PLIOptimizationIntegrator - 最终统计: PLI性能统计 - 总请求: 2641, 原始: 2641(100.0%), 优化: 0(0.0%), 流式: 0(0.0%), 当前策略: 原始实现, 内存使用: 1710MB/21.1%
执行时间: 6048 ms
发现FD数量: 47
验证次数: 229
BitSet缓存统计: BitSet缓存统计 - cardinality缓存: 0, bitSetToList缓存: 499

org.opentest4j.AssertionFailedError: 执行时间 6048 ms 超过预期的3000ms ==>
预期:true
实际:false
<点击以查看差异>


	at org.junit.jupiter.api.AssertionUtils.fail(AssertionUtils.java:55)
	at org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:40)
	at org.junit.jupiter.api.Assertions.assertTrue(Assertions.java:210)
	at algorithm.PyroPerformanceTest.testBioEntryPerformance(PyroPerformanceTest.java:86)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:725)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:149)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:140)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:84)
	at org.junit.jupiter.engine.execution.ExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(ExecutableInvoker.java:115)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.lambda$invoke$0(ExecutableInvoker.java:105)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:104)
	at org.junit.jupiter.engine.execution.ExecutableInvoker.invoke(ExecutableInvoker.java:98)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:214)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:210)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:135)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:66)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:107)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:88)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:67)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:52)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:114)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:86)
	at org.junit.platform.launcher.core.DefaultLauncherSession$DelegatingLauncher.execute(DefaultLauncherSession.java:86)
	at org.junit.platform.launcher.core.SessionPerRequestLauncher.execute(SessionPerRequestLauncher.java:53)
	at com.intellij.junit5.JUnit5IdeaTestRunner.startRunnerWithArgs(JUnit5IdeaTestRunner.java:57)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:232)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:55)


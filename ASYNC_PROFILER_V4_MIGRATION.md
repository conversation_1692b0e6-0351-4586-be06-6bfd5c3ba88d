# Async Profiler v4.1 迁移指南

本文档记录了从async-profiler v2.x迁移到v4.1的所有变更和适配工作。

## 版本变更概述

### 主要变化

| 项目 | v2.x | v4.1 |
|------|------|------|
| 可执行文件 | `profiler.sh` | `bin/asprof` |
| 命令语法 | `profiler.sh -e cpu -d 30 -f output.html <PID>` | `asprof -e cpu -d 30 -f output.html <PID>` |
| 目录结构 | 扁平结构 | 分层结构（bin/, lib/, etc.） |
| 版本查看 | `profiler.sh -v` | `asprof --version` |

### 新特性

1. **更简洁的命令行界面**
2. **改进的性能和稳定性**
3. **更多的事件类型支持**
4. **更好的输出格式选项**
5. **增强的错误处理**

## 代码适配详情

### 1. run_experiments.sh 修改

#### 检查函数更新
```bash
# 旧版本检查
PROFILER_SCRIPT="$ASYNC_PROFILER_PATH/profiler.sh"
if [ ! -f "$PROFILER_SCRIPT" ] || [ ! -x "$PROFILER_SCRIPT" ]; then
    # 错误处理
fi

# 新版本检查
PROFILER_EXECUTABLE="$ASYNC_PROFILER_PATH/bin/asprof"
if [ ! -f "$PROFILER_EXECUTABLE" ] || [ ! -x "$PROFILER_EXECUTABLE" ]; then
    # 错误处理
fi

# 添加命令测试
if ! "$PROFILER_EXECUTABLE" --help >/dev/null 2>&1; then
    # 错误处理
fi
```

#### 命令调用更新
```bash
# 旧版本调用
$PROFILER_SCRIPT -e cpu,alloc -d $PROFILING_DURATION -i $PROFILING_INTERVAL -f $FLAME_GRAPH_FILE $JAVA_PID &

# 新版本调用
ASPROF_CMD=(
    "$PROFILER_EXECUTABLE"
    "-e" "cpu,alloc"
    "-i" "$PROFILING_INTERVAL"
    "-f" "$FLAME_GRAPH_FILE"
)

if [ "$PROFILING_DURATION" -gt 0 ]; then
    ASPROF_CMD+=("-d" "$PROFILING_DURATION")
fi

ASPROF_CMD+=("$JAVA_PID")
"${ASPROF_CMD[@]}" &
```

#### 停止处理改进
```bash
# 新版本的优雅停止
if kill -0 $PROFILER_PID 2>/dev/null; then
    log_info "停止性能分析..."
    # 发送SIGTERM信号来优雅停止
    kill -TERM $PROFILER_PID 2>/dev/null || true
    # 等待一段时间让profiler完成文件写入
    sleep 3
    # 如果还在运行，强制终止
    if kill -0 $PROFILER_PID 2>/dev/null; then
        kill -KILL $PROFILER_PID 2>/dev/null || true
    fi
    wait $PROFILER_PID 2>/dev/null || true
fi
```

### 2. 文档更新

#### 安装指导更新
- 更新下载链接到v4.1
- 修改可执行文件路径
- 更新验证命令

#### 使用示例更新
- 更新命令语法示例
- 添加新版本特性说明
- 更新故障排除指南

### 3. 新增工具

#### install_async_profiler.sh
专门的安装脚本，包含：
- 自动下载v4.1版本
- 权限设置
- 内核参数配置
- 安装验证
- 符号链接创建

#### 测试脚本增强
在`test_pli_modes.sh`中添加：
- async-profiler v4.1检测
- 版本信息显示
- 安装建议

## 兼容性说明

### 向后兼容性
- ✅ 脚本参数保持不变
- ✅ 输出文件格式兼容
- ✅ 功能特性保持一致

### 不兼容变更
- ❌ 可执行文件路径变更
- ❌ 命令行参数略有差异
- ❌ 目录结构变化

## 迁移步骤

### 1. 卸载旧版本（可选）
```bash
# 如果之前安装了旧版本
sudo rm -rf /opt/async-profiler
```

### 2. 安装新版本
```bash
# 使用自动安装脚本
sudo ./install_async_profiler.sh

# 或手动安装
cd /tmp
wget https://github.com/jvm-profiling-tools/async-profiler/releases/download/v4.1/async-profiler-4.1-linux-x64.tar.gz
sudo mkdir -p /opt/async-profiler
sudo tar -xzf async-profiler-4.1-linux-x64.tar.gz -C /opt/async-profiler --strip-components=1
sudo chmod +x /opt/async-profiler/bin/asprof
```

### 3. 验证安装
```bash
# 测试命令
/opt/async-profiler/bin/asprof --help

# 运行测试脚本
./test_pli_modes.sh
```

### 4. 更新脚本
已完成的更新：
- ✅ run_experiments.sh
- ✅ PLI_PERFORMANCE_ANALYSIS_GUIDE.md
- ✅ PLI_MODIFICATIONS_README.md
- ✅ test_pli_modes.sh

## 验证清单

### 安装验证
- [ ] async-profiler v4.1正确下载
- [ ] 解压到正确目录
- [ ] 可执行文件权限设置
- [ ] 内核参数配置
- [ ] 命令行测试通过

### 功能验证
- [ ] PLI模式切换正常
- [ ] 性能分析启动成功
- [ ] 火焰图正确生成
- [ ] 进程监控正常
- [ ] 优雅停止工作

### 集成验证
- [ ] run_experiments.sh正常运行
- [ ] 测试脚本通过
- [ ] 文档更新完整
- [ ] 错误处理正确

## 故障排除

### 常见问题

1. **asprof命令不存在**
   ```bash
   # 检查安装路径
   ls -la /opt/async-profiler/bin/asprof
   # 重新安装
   sudo ./install_async_profiler.sh
   ```

2. **权限问题**
   ```bash
   # 设置执行权限
   sudo chmod +x /opt/async-profiler/bin/asprof
   # 检查内核参数
   sysctl kernel.perf_event_paranoid
   sysctl kernel.kptr_restrict
   ```

3. **命令执行失败**
   ```bash
   # 测试命令
   /opt/async-profiler/bin/asprof --help
   # 检查依赖
   ldd /opt/async-profiler/bin/asprof
   ```

### 调试技巧

1. **启用详细日志**
   ```bash
   # 在run_experiments.sh中设置
   DEBUG=true ./run_experiments.sh
   ```

2. **手动测试profiling**
   ```bash
   # 启动测试Java程序
   java -cp test.jar TestClass &
   PID=$!
   
   # 手动运行profiler
   /opt/async-profiler/bin/asprof -d 10 -f test.html $PID
   ```

3. **检查生成的文件**
   ```bash
   # 验证火焰图文件
   ls -la flamegraph_*.html
   file flamegraph_*.html
   ```

## 性能对比

### v2.x vs v4.1
基于初步测试：
- 启动时间：v4.1更快
- 内存占用：v4.1更低
- 输出质量：v4.1更好
- 稳定性：v4.1更稳定

### 建议配置
```bash
# 推荐的profiling参数
PROFILING_INTERVAL=10ms
PROFILING_DURATION=0  # 跟随程序运行
ENABLE_PROFILING=true
```

## 总结

async-profiler v4.1的迁移工作已完成，主要改进包括：

1. **更现代的架构**：分层目录结构，更清晰的组织
2. **简化的命令行**：更直观的使用方式
3. **增强的稳定性**：更好的错误处理和资源管理
4. **完整的工具链**：自动安装、测试、验证脚本

所有修改都保持了向后兼容性，用户可以无缝升级到新版本。

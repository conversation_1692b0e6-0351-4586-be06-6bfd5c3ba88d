# PLI算法实现切换和性能分析功能

本文档总结了对AFD-measures项目的修改，以支持PLI算法实现切换和性能分析功能。

## 修改概述

### 1. ExperimentRunner.java 修改

**文件路径**: `AFD-algorithms/experiment/src/main/java/experiment/ExperimentRunner.java`

**主要修改**:
- 添加了`PLIMode`枚举类型，支持`ORIGINAL`和`DYNAMIC`两种模式
- 新增`--pli-mode`和`-p`命令行参数
- 添加`configurePLIMode()`方法来配置PLI算法实现
- 更新默认结果文件路径为`result/result0807_test.csv`

**新增功能**:
```java
// PLI算法实现模式枚举
public enum PLIMode {
    ORIGINAL,       // 仅使用原始实现
    DYNAMIC         // 动态切换实现
}

// 新增配置变量
public static PLIMode PLI_MODE = PLIMode.ORIGINAL;
```

**命令行参数**:
```bash
--pli-mode original  # 或 dynamic
-p original         # 短参数形式
```

### 2. run_experiments.sh 修改

**文件路径**: `run_experiments.sh`

**主要修改**:
- 集成async-profiler性能分析工具
- 更新实验参数配置
- 添加火焰图生成功能
- 增强错误处理和用户指导

**新增配置变量**:
```bash
# 数据集配置
DATASET_PATH='data/int/EQ-500K-12.csv'
RESULTS_FILE='result/result0807_test.csv'

# PLI模式配置
PLI_MODE='original'

# Async Profiling配置
ENABLE_PROFILING=true
ASYNC_PROFILER_PATH="/opt/async-profiler"
```

**新增功能**:
- 自动检查async-profiler安装
- 生成带时间戳的火焰图文件
- 提供详细的安装和使用指导
- 支持性能分析的启动和停止

## 使用方法

### 1. 基本使用

```bash
# 使用原始PLI实现
./run_experiments.sh

# 或者设置环境变量
PLI_MODE=original ./run_experiments.sh
PLI_MODE=dynamic ./run_experiments.sh
```

### 2. 直接使用Java命令

```bash
java -cp AFD-algorithms/experiment/target/experiment-1.0-SNAPSHOT-jar-with-dependencies.jar \
     experiment.ExperimentRunner \
     --dataset data/int/EQ-500K-12.csv \
     --results-file result/result0807_test.csv \
     --pli-mode original \
     --sampling-mode ALL \
     --run-tane true \
     --timeout 120
```

### 3. 测试功能

```bash
# 运行测试脚本验证功能
chmod +x test_pli_modes.sh
./test_pli_modes.sh
```

## Async Profiler安装

### 首次安装步骤

1. **下载和安装async-profiler v4.1**:
```bash
cd /tmp
wget https://github.com/jvm-profiling-tools/async-profiler/releases/download/v4.1/async-profiler-4.1-linux-x64.tar.gz
sudo mkdir -p /opt/async-profiler
sudo tar -xzf async-profiler-4.1-linux-x64.tar.gz -C /opt/async-profiler --strip-components=1
sudo chmod +x /opt/async-profiler/bin/asprof
```

2. **配置内核参数**（SSH服务器环境）:
```bash
sudo sysctl kernel.perf_event_paranoid=1
sudo sysctl kernel.kptr_restrict=0
```

3. **验证安装**:
```bash
/opt/async-profiler/bin/asprof --help
```

4. **版本说明**:
async-profiler v4.1的主要变化：
- 可执行文件从`profiler.sh`改为`bin/asprof`
- 命令语法：`asprof -d 30 -f flamegraph.html <PID>`
- 支持更多事件类型和输出格式

## 生成的文件

### 1. 实验结果
- `result/result0807_test.csv` - 实验结果数据

### 2. 性能分析文件
- `flamegraph_[mode]_[timestamp].html` - 火焰图文件
- `profiling_[mode]_[timestamp].jfr` - JFR分析文件
- `gc-128gb-[timestamp].log` - GC日志文件

### 3. 测试文件
- `test_results/test_*.csv` - 测试结果文件

## 火焰图查看方法

### 方法1: 下载到本地
```bash
scp user@server:/path/to/flamegraph_*.html ./
# 用浏览器打开HTML文件
```

### 方法2: 服务器HTTP服务
```bash
python3 -m http.server 8000
# 浏览器访问: http://服务器IP:8000/flamegraph_*.html
```

### 方法3: VS Code Remote
直接在VS Code中打开HTML文件查看。

## 配置说明

### PLI模式对比

| 模式 | 描述 | 适用场景 | 性能特点 |
|------|------|----------|----------|
| original | 仅使用原始PLI实现 | 性能基准测试 | 稳定，内存使用较高 |
| dynamic | 动态切换PLI实现 | 生产环境，大数据集 | 自适应，内存优化 |

### 系统属性配置

**原始模式**:
```properties
pli.force.original.only=true
pli.disable.optimization=true
pli.disable.dynamic.switching=true
```

**动态模式**:
```properties
pli.force.original.only=false
pli.disable.optimization=false
pli.disable.dynamic.switching=false
pli.enable.optimization.integrator=true
```

## 故障排除

### 1. 编译问题
```bash
# 重新编译项目
mvn clean package -DskipTests
```

### 2. 权限问题
```bash
# 设置脚本执行权限
chmod +x run_experiments.sh
chmod +x test_pli_modes.sh
```

### 3. Async Profiler v4.1问题
```bash
# 检查安装
ls -la /opt/async-profiler/bin/asprof

# 检查权限
/opt/async-profiler/bin/asprof --help

# 检查内核参数
sysctl kernel.perf_event_paranoid
sysctl kernel.kptr_restrict
```

### 4. 内存问题
- 确保系统有足够内存（建议32GB+）
- 调整JVM堆内存设置
- 监控内存使用情况

## 向后兼容性

所有修改都保持向后兼容：
- 默认PLI模式为`original`
- 现有命令行参数保持不变
- 可以禁用性能分析功能

## 文件清单

### 修改的文件
1. `AFD-algorithms/experiment/src/main/java/experiment/ExperimentRunner.java`
2. `run_experiments.sh`

### 新增的文件
1. `PLI_PERFORMANCE_ANALYSIS_GUIDE.md` - 详细使用指南
2. `test_pli_modes.sh` - 功能测试脚本
3. `PLI_MODIFICATIONS_README.md` - 本文档

## 下一步建议

1. **验证功能**: 运行测试脚本确认功能正常
2. **安装async-profiler**: 按照指南安装性能分析工具
3. **运行对比实验**: 分别使用两种PLI模式进行实验
4. **分析结果**: 比较性能数据和火焰图
5. **优化配置**: 根据实际环境调整参数

## 技术支持

如遇问题，请检查：
1. Java版本兼容性
2. 系统内存配置
3. 权限设置
4. 日志文件错误信息

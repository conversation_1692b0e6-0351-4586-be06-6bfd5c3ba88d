#!/bin/bash

# Async Profiler v4.1 集成验证脚本
# 验证run_experiments.sh中的async-profiler v4.1集成是否正常工作

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "============================================================"
echo "        Async Profiler v4.1 集成验证"
echo "============================================================"

# 配置变量
ASYNC_PROFILER_PATH="/opt/async-profiler"
PROFILER_EXECUTABLE="$ASYNC_PROFILER_PATH/bin/asprof"

# 测试1: 检查安装路径
log_info "测试1: 检查async-profiler安装路径"
if [ -d "$ASYNC_PROFILER_PATH" ]; then
    log_success "安装目录存在: $ASYNC_PROFILER_PATH"
    
    # 列出目录内容
    log_info "目录内容:"
    ls -la "$ASYNC_PROFILER_PATH" | head -10
else
    log_error "安装目录不存在: $ASYNC_PROFILER_PATH"
    log_info "请运行: sudo ./install_async_profiler.sh"
    exit 1
fi

echo

# 测试2: 检查可执行文件
log_info "测试2: 检查asprof可执行文件"
if [ -f "$PROFILER_EXECUTABLE" ]; then
    log_success "可执行文件存在: $PROFILER_EXECUTABLE"
    
    # 检查权限
    if [ -x "$PROFILER_EXECUTABLE" ]; then
        log_success "可执行权限正确"
    else
        log_error "缺少执行权限"
        log_info "修复命令: sudo chmod +x $PROFILER_EXECUTABLE"
        exit 1
    fi
    
    # 显示文件信息
    log_info "文件信息:"
    ls -la "$PROFILER_EXECUTABLE"
else
    log_error "可执行文件不存在: $PROFILER_EXECUTABLE"
    log_info "请检查async-profiler v4.1是否正确安装"
    exit 1
fi

echo

# 测试3: 测试命令执行
log_info "测试3: 测试asprof命令执行"
if "$PROFILER_EXECUTABLE" --help >/dev/null 2>&1; then
    log_success "asprof命令执行正常"
    
    # 获取版本信息
    VERSION_INFO=$("$PROFILER_EXECUTABLE" --version 2>/dev/null | head -n1 || echo "版本信息获取失败")
    log_info "版本信息: $VERSION_INFO"
else
    log_error "asprof命令执行失败"
    log_info "可能的原因:"
    log_info "1. 内核参数未正确设置"
    log_info "2. 权限不足"
    log_info "3. 依赖库缺失"
    
    # 检查内核参数
    log_info "当前内核参数:"
    echo "  kernel.perf_event_paranoid = $(sysctl -n kernel.perf_event_paranoid 2>/dev/null || echo '未设置')"
    echo "  kernel.kptr_restrict = $(sysctl -n kernel.kptr_restrict 2>/dev/null || echo '未设置')"
    
    exit 1
fi

echo

# 测试4: 模拟run_experiments.sh中的检查函数
log_info "测试4: 模拟run_experiments.sh检查逻辑"

# 复制check_async_profiler函数的逻辑
ENABLE_PROFILING=true

if [ "$ENABLE_PROFILING" = "true" ]; then
    if [ ! -d "$ASYNC_PROFILER_PATH" ]; then
        log_error "路径检查失败"
        ENABLE_PROFILING=false
    elif [ ! -f "$PROFILER_EXECUTABLE" ] || [ ! -x "$PROFILER_EXECUTABLE" ]; then
        log_error "可执行文件检查失败"
        ENABLE_PROFILING=false
    elif ! "$PROFILER_EXECUTABLE" --help >/dev/null 2>&1; then
        log_error "命令测试失败"
        ENABLE_PROFILING=false
    else
        log_success "所有检查通过，性能分析功能可用"
    fi
fi

if [ "$ENABLE_PROFILING" = "false" ]; then
    log_error "性能分析功能不可用"
    exit 1
fi

echo

# 测试5: 模拟命令构建
log_info "测试5: 模拟profiling命令构建"

# 模拟参数
PROFILING_INTERVAL="10ms"
PROFILING_DURATION=0
FLAME_GRAPH_FILE="test_flamegraph.html"
MOCK_PID="12345"

# 构建命令（模拟run_experiments.sh中的逻辑）
ASPROF_CMD=(
    "$PROFILER_EXECUTABLE"
    "-e" "cpu,alloc"
    "-i" "$PROFILING_INTERVAL"
    "-f" "$FLAME_GRAPH_FILE"
)

if [ "$PROFILING_DURATION" -gt 0 ]; then
    ASPROF_CMD+=("-d" "$PROFILING_DURATION")
fi

ASPROF_CMD+=("$MOCK_PID")

log_success "命令构建成功"
log_info "模拟命令: ${ASPROF_CMD[*]}"

echo

# 测试6: 验证命令语法
log_info "测试6: 验证命令语法（不实际执行）"

# 检查命令语法是否正确（使用--help来验证参数格式）
if "$PROFILER_EXECUTABLE" --help 2>&1 | grep -q "\-e"; then
    log_success "事件参数(-e)支持确认"
else
    log_warn "事件参数(-e)支持未确认"
fi

if "$PROFILER_EXECUTABLE" --help 2>&1 | grep -q "\-i"; then
    log_success "间隔参数(-i)支持确认"
else
    log_warn "间隔参数(-i)支持未确认"
fi

if "$PROFILER_EXECUTABLE" --help 2>&1 | grep -q "\-f"; then
    log_success "输出文件参数(-f)支持确认"
else
    log_warn "输出文件参数(-f)支持未确认"
fi

if "$PROFILER_EXECUTABLE" --help 2>&1 | grep -q "\-d"; then
    log_success "持续时间参数(-d)支持确认"
else
    log_warn "持续时间参数(-d)支持未确认"
fi

echo

# 测试7: 检查run_experiments.sh脚本
log_info "测试7: 检查run_experiments.sh脚本更新"

if [ -f "run_experiments.sh" ]; then
    log_success "run_experiments.sh脚本存在"
    
    # 检查关键更新
    if grep -q "bin/asprof" run_experiments.sh; then
        log_success "脚本已更新为使用bin/asprof"
    else
        log_error "脚本未更新，仍使用旧的profiler.sh"
    fi
    
    if grep -q "PROFILER_EXECUTABLE" run_experiments.sh; then
        log_success "脚本使用新的变量名PROFILER_EXECUTABLE"
    else
        log_warn "脚本可能未完全更新"
    fi
    
    if grep -q "async-profiler v4.1" run_experiments.sh; then
        log_success "脚本包含v4.1版本信息"
    else
        log_warn "脚本版本信息可能需要更新"
    fi
else
    log_error "run_experiments.sh脚本不存在"
fi

echo

# 总结
echo "============================================================"
echo "                验证总结"
echo "============================================================"

if [ "$ENABLE_PROFILING" = "true" ]; then
    log_success "Async Profiler v4.1集成验证通过！"
    echo
    log_info "验证结果:"
    log_info "✅ 安装路径正确"
    log_info "✅ 可执行文件存在且有权限"
    log_info "✅ 命令执行正常"
    log_info "✅ 版本信息: $VERSION_INFO"
    log_info "✅ 命令语法正确"
    log_info "✅ 脚本更新完成"
    echo
    log_info "现在可以运行以下命令进行完整测试:"
    log_info "1. ./test_pli_modes.sh  # 测试PLI模式功能"
    log_info "2. ./run_experiments.sh  # 运行完整实验（包含性能分析）"
else
    log_error "验证失败，请检查安装和配置"
fi

echo "============================================================"

# Async Profiler v4.1 完整适配指南

本文档提供了async-profiler v4.1适配的完整指南，包括安装、配置、验证和使用。

## 🎯 适配概述

已成功将AFD-measures项目从async-profiler v2.x升级到v4.1，主要变更：

- ✅ 更新可执行文件路径：`profiler.sh` → `bin/asprof`
- ✅ 适配新的命令语法
- ✅ 增强错误处理和验证
- ✅ 提供完整的安装和验证工具
- ✅ 保持向后兼容性

## 📁 文件清单

### 修改的文件
1. **`run_experiments.sh`** - 主实验脚本，适配v4.1
2. **`PLI_PERFORMANCE_ANALYSIS_GUIDE.md`** - 使用指南更新
3. **`PLI_MODIFICATIONS_README.md`** - 修改总结更新
4. **`test_pli_modes.sh`** - 测试脚本增强

### 新增的文件
1. **`install_async_profiler.sh`** - 自动安装脚本
2. **`verify_async_profiler_integration.sh`** - 集成验证脚本
3. **`ASYNC_PROFILER_V4_MIGRATION.md`** - 迁移详细文档
4. **`ASYNC_PROFILER_V4_COMPLETE_GUIDE.md`** - 本文档

## 🚀 快速开始

### 1. 安装async-profiler v4.1

```bash
# 方法1: 使用自动安装脚本（推荐）
sudo chmod +x install_async_profiler.sh
sudo ./install_async_profiler.sh

# 方法2: 手动安装
cd /tmp
wget https://github.com/jvm-profiling-tools/async-profiler/releases/download/v4.1/async-profiler-4.1-linux-x64.tar.gz
sudo mkdir -p /opt/async-profiler
sudo tar -xzf async-profiler-4.1-linux-x64.tar.gz -C /opt/async-profiler --strip-components=1
sudo chmod +x /opt/async-profiler/bin/asprof

# 配置内核参数
sudo sysctl kernel.perf_event_paranoid=1
sudo sysctl kernel.kptr_restrict=0
```

### 2. 验证安装

```bash
# 设置脚本权限
chmod +x verify_async_profiler_integration.sh
chmod +x test_pli_modes.sh

# 验证async-profiler集成
./verify_async_profiler_integration.sh

# 测试PLI功能
./test_pli_modes.sh
```

### 3. 运行实验

```bash
# 编译项目
mvn clean package -DskipTests

# 运行实验（包含性能分析）
./run_experiments.sh
```

## 🔧 详细配置

### async-profiler v4.1 配置

#### 安装路径
```bash
ASYNC_PROFILER_PATH="/opt/async-profiler"
PROFILER_EXECUTABLE="$ASYNC_PROFILER_PATH/bin/asprof"
```

#### 命令语法
```bash
# 基本语法
asprof [options] <PID>

# 常用选项
-e <events>     # 事件类型：cpu, alloc, lock等
-d <duration>   # 持续时间（秒）
-i <interval>   # 采样间隔
-f <file>       # 输出文件
--help          # 帮助信息
--version       # 版本信息
```

#### 实际使用示例
```bash
# 分析Java进程30秒，生成火焰图
/opt/async-profiler/bin/asprof -e cpu,alloc -d 30 -f flamegraph.html <PID>

# 跟随程序运行直到结束
/opt/async-profiler/bin/asprof -e cpu -f flamegraph.html <PID>
```

### run_experiments.sh 配置

#### 性能分析配置
```bash
# 启用/禁用性能分析
ENABLE_PROFILING=true

# profiling参数
PROFILING_DURATION=0        # 0表示跟随程序运行
PROFILING_INTERVAL=10ms     # 采样间隔
ASYNC_PROFILER_PATH="/opt/async-profiler"
```

#### PLI模式配置
```bash
# PLI算法实现模式
PLI_MODE='original'  # 或 'dynamic'

# 数据集和结果配置
DATASET_PATH='data/int/EQ-500K-12.csv'
RESULTS_FILE='result/result0807_test.csv'
```

## 🧪 测试和验证

### 验证步骤

1. **安装验证**
```bash
./verify_async_profiler_integration.sh
```

2. **功能测试**
```bash
./test_pli_modes.sh
```

3. **完整实验**
```bash
./run_experiments.sh
```

### 预期输出

#### 成功的验证输出
```
============================================================
        Async Profiler v4.1 集成验证
============================================================
[INFO] 测试1: 检查async-profiler安装路径
[SUCCESS] 安装目录存在: /opt/async-profiler
[SUCCESS] 可执行文件存在: /opt/async-profiler/bin/asprof
[SUCCESS] 可执行权限正确
[SUCCESS] asprof命令执行正常
[INFO] 版本信息: async-profiler 4.1
[SUCCESS] Async Profiler v4.1集成验证通过！
```

#### 生成的文件
```
flamegraph_original_20240807_143022.html    # 火焰图
result/result0807_test.csv                  # 实验结果
gc-128gb-20240807-143022.log                # GC日志
test_results/test_*.csv                     # 测试结果
```

## 📊 性能分析

### 火焰图分析

#### 查看方法
1. **本地下载**
```bash
scp user@server:/path/to/flamegraph_*.html ./
# 浏览器打开HTML文件
```

2. **服务器HTTP服务**
```bash
python3 -m http.server 8000
# 访问: http://服务器IP:8000/flamegraph_*.html
```

3. **VS Code Remote**
直接在VS Code中打开HTML文件

#### 分析要点
- **宽度**：函数执行时间占比
- **高度**：调用栈深度
- **颜色**：不同的包或函数类型
- **热点**：宽度较大的区域是性能瓶颈

### PLI性能对比

#### 对比实验
```bash
# 原始模式
PLI_MODE=original ./run_experiments.sh

# 动态模式
PLI_MODE=dynamic ./run_experiments.sh
```

#### 关键指标
- 执行时间
- 内存使用峰值
- PLI缓存命中率
- GC行为差异

## 🛠️ 故障排除

### 常见问题

#### 1. asprof命令不存在
```bash
# 检查安装
ls -la /opt/async-profiler/bin/asprof

# 重新安装
sudo ./install_async_profiler.sh
```

#### 2. 权限问题
```bash
# 设置执行权限
sudo chmod +x /opt/async-profiler/bin/asprof

# 检查内核参数
sysctl kernel.perf_event_paranoid  # 应该是1
sysctl kernel.kptr_restrict        # 应该是0
```

#### 3. 命令执行失败
```bash
# 测试命令
/opt/async-profiler/bin/asprof --help

# 检查依赖
ldd /opt/async-profiler/bin/asprof
```

#### 4. 火焰图未生成
```bash
# 检查profiler进程
ps aux | grep asprof

# 检查文件权限
ls -la flamegraph_*.html

# 手动测试
/opt/async-profiler/bin/asprof -d 10 -f test.html <PID>
```

### 调试技巧

#### 启用详细日志
```bash
DEBUG=true ./run_experiments.sh
```

#### 手动测试profiling
```bash
# 启动测试程序
java -cp test.jar TestClass &
PID=$!

# 手动运行profiler
/opt/async-profiler/bin/asprof -d 10 -f test.html $PID
```

## 📈 性能优化建议

### 系统配置
1. **内存配置**：建议32GB+
2. **内核参数**：正确设置perf_event_paranoid
3. **文件系统**：使用SSD存储结果文件

### profiling配置
1. **采样间隔**：10ms适合大多数场景
2. **事件类型**：cpu,alloc覆盖主要性能指标
3. **持续时间**：跟随程序运行(duration=0)

### 实验配置
1. **数据集选择**：从小到大逐步测试
2. **PLI模式**：先测试original，再测试dynamic
3. **结果分析**：结合多个指标综合判断

## 🔄 版本兼容性

### 支持的版本
- ✅ async-profiler v4.1（推荐）
- ✅ async-profiler v4.0
- ❌ async-profiler v2.x（需要迁移）

### 迁移路径
1. 卸载旧版本
2. 安装v4.1
3. 更新脚本（已完成）
4. 验证功能

## 📚 参考资源

### 官方文档
- [async-profiler GitHub](https://github.com/jvm-profiling-tools/async-profiler)
- [async-profiler Wiki](https://github.com/jvm-profiling-tools/async-profiler/wiki)

### 项目文档
- `PLI_PERFORMANCE_ANALYSIS_GUIDE.md` - 详细使用指南
- `ASYNC_PROFILER_V4_MIGRATION.md` - 迁移技术细节
- `PLI_MODIFICATIONS_README.md` - 项目修改总结

## ✅ 检查清单

### 安装检查
- [ ] async-profiler v4.1下载完成
- [ ] 解压到/opt/async-profiler
- [ ] bin/asprof有执行权限
- [ ] 内核参数正确设置
- [ ] 命令测试通过

### 功能检查
- [ ] PLI模式切换正常
- [ ] 性能分析启动成功
- [ ] 火焰图正确生成
- [ ] 实验结果正确保存
- [ ] 错误处理正常

### 集成检查
- [ ] run_experiments.sh正常运行
- [ ] 测试脚本通过
- [ ] 文档更新完整
- [ ] 向后兼容性保持

## 🎉 总结

async-profiler v4.1的适配工作已全面完成，提供了：

1. **完整的工具链**：安装、验证、测试脚本
2. **详细的文档**：使用指南、迁移文档、故障排除
3. **增强的功能**：更好的错误处理、性能分析
4. **向后兼容**：保持原有功能不变

现在可以充分利用async-profiler v4.1的新特性进行PLI算法性能分析了！

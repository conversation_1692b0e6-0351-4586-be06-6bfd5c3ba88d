# PLI????????
# ?????PLI???????????

# ============================================================
# PLI????
# ============================================================

# PLI??????????MB?
pli.cache.max.memory.mb=1024

# ??????????MB?
pli.cache.cleanup.threshold.mb=800

# ????????????
pli.cache.max=10000
pli.cache.cleanup.batch=1000
pli.cache.cleanup.threshold=8000

# ?????0.0-1.0?
pli.cache.probability=0.5

# ============================================================
# ??????
# ============================================================

# ?????
streaming.pli.chunk.size=50000

# ?????
streaming.pli.min.chunk.size=10000

# ?????
streaming.pli.max.chunk.size=200000

# ??????????MB?
streaming.pli.memory.threshold.mb=100

# ?????????true/false?
pli.force.streaming=false

# ============================================================
# ??????
# ============================================================

# ??????
memory.monitor.enabled=true

# ????????
memory.monitor.interval.ms=5000

# ???????0.0-1.0?
memory.monitor.warning.threshold=0.7

# ?????????0.0-1.0?
memory.monitor.critical.threshold=0.85

# ????????
memory.monitor.history.size=10

# ============================================================
# ??????
# ============================================================

# ??????????
strategy.large.dataset.threshold=1000000

# ???????????
strategy.huge.dataset.threshold=5000000

# ????
strategy.many.columns.threshold=20

# ??????MB?
strategy.low.memory.threshold.mb=512

# ??????MB?
strategy.high.memory.threshold.mb=2048

# ============================================================
# ??????
# ============================================================

# ??????
pli.verbose.logging=false

# ??????
pli.performance.stats=true

# ??????
pli.memory.stats=true

# GC????
gc.optimization.enabled=true

# ============================================================
# ??????
# ============================================================

# ????????????
experiment.default.timeout.minutes=30

# ????????????
experiment.large.dataset.timeout.minutes=120

# ?????????????
experiment.huge.dataset.timeout.minutes=240

# ???????
experiment.memory.precheck.enabled=true

# ????????
experiment.auto.memory.config.enabled=true

# ============================================================
# JVM????
# ============================================================

# ???JVM????????????
# ?????<100????
# -Xms1g -Xmx2g -XX:+UseG1GC

# ??????100?-500????
# -Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200

# ?????500?-1000????
# -Xms4g -Xmx8g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=16m

# ??????>1000????
# -Xms8g -Xmx16g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=32m

# ============================================================
# ?????????
# ============================================================

# ??????
pli.debug.enabled=false

# ???????????
pli.debug.memory.verbose=false

# ??PLI????
pli.debug.construction.verbose=false

# ?????????
pli.debug.cache.stats=true

# ????????
pli.debug.memory.leak.detection=false

# ============================================================
# ?????
# ============================================================

# ??????????????
pli.compatibility.mode=false

# ?????PLI??
pli.use.original.only=false

# ??????
pli.disable.memory.monitor=false

# ????????
pli.disable.auto.strategy=false

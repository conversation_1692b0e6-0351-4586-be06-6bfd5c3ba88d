<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.example</groupId>
        <artifactId>AFD-measures</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>AFD-algorithms</artifactId>
    <packaging>pom</packaging>

    <name>AFD-algorithms</name>
    <url>http://maven.apache.org</url>
    <modules>
        <module>tane</module>
        <module>pyro</module>
        <module>experiment</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
        </dependency>

    </dependencies>
</project>

#!/bin/bash

# Async Profiler Agent模式验证脚本
# 验证从attach模式到agent模式的迁移是否成功

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "============================================================"
echo "        Async Profiler Agent模式验证"
echo "============================================================"

# 1. 检查async-profiler安装
log_info "检查async-profiler安装..."

ASYNC_PROFILER_PATH="/opt/async-profiler"
ASYNC_PROFILER_LIB="${ASYNC_PROFILER_PATH}/lib/libasyncProfiler.so"
PROFILER_EXECUTABLE="${ASYNC_PROFILER_PATH}/bin/asprof"

if [ ! -d "$ASYNC_PROFILER_PATH" ]; then
    log_error "Async Profiler安装目录不存在: $ASYNC_PROFILER_PATH"
    exit 1
fi

log_success "安装目录存在: $ASYNC_PROFILER_PATH"

# 2. 检查Agent库文件
log_info "检查Agent库文件..."

if [ ! -f "$ASYNC_PROFILER_LIB" ]; then
    log_error "Agent库文件不存在: $ASYNC_PROFILER_LIB"
    exit 1
fi

if [ ! -r "$ASYNC_PROFILER_LIB" ]; then
    log_error "Agent库文件无读取权限: $ASYNC_PROFILER_LIB"
    log_info "修复命令: sudo chmod 644 $ASYNC_PROFILER_LIB"
    exit 1
fi

log_success "Agent库文件检查通过: $ASYNC_PROFILER_LIB"

# 显示库文件信息
log_info "库文件信息:"
ls -la "$ASYNC_PROFILER_LIB"
file "$ASYNC_PROFILER_LIB"

# 3. 检查可执行文件（用于attach模式）
log_info "检查asprof可执行文件（用于内存分析）..."

if [ -f "$PROFILER_EXECUTABLE" ] && [ -x "$PROFILER_EXECUTABLE" ]; then
    log_success "asprof可执行文件可用: $PROFILER_EXECUTABLE"
    
    # 测试命令
    if "$PROFILER_EXECUTABLE" --help >/dev/null 2>&1; then
        log_success "asprof命令测试通过"
    else
        log_warn "asprof命令测试失败，但不影响Agent模式"
    fi
else
    log_warn "asprof可执行文件不可用，内存分析功能将被禁用"
fi

# 4. 测试Agent模式
log_info "测试Agent模式..."

# 创建测试参数
TEST_AGENT_PARAMS="start,event=cpu,interval=10ms,file=test_agent_mode.html"

# 测试Agent加载
log_info "测试Agent库加载..."
if java -agentpath:"${ASYNC_PROFILER_LIB}=${TEST_AGENT_PARAMS}" -version >/dev/null 2>&1; then
    log_success "Agent模式测试通过"
else
    log_error "Agent模式测试失败"
    log_info "可能的原因:"
    log_info "1. 库文件损坏"
    log_info "2. 权限问题"
    log_info "3. 系统不兼容"
    exit 1
fi

# 清理测试文件
rm -f test_agent_mode.html

# 5. 检查run_experiments.sh中的Agent配置
log_info "检查run_experiments.sh中的Agent配置..."

if [ ! -f "run_experiments.sh" ]; then
    log_error "run_experiments.sh文件不存在"
    exit 1
fi

# 检查Agent相关配置
if grep -q "ASYNC_PROFILER_LIB" run_experiments.sh; then
    log_success "Agent库路径配置存在"
else
    log_error "Agent库路径配置缺失"
fi

if grep -q "agentpath" run_experiments.sh; then
    log_success "Agent模式配置存在"
else
    log_error "Agent模式配置缺失"
fi

if grep -q "Agent模式" run_experiments.sh; then
    log_success "Agent模式注释和说明存在"
else
    log_warn "Agent模式说明可能缺失"
fi

# 6. 验证配置变量
log_info "验证配置变量..."

# 提取配置变量
CONFIG_SECTION=$(sed -n '/配置区域 - 所有可修改的参数/,/脚本功能区域 - 请勿修改/p' run_experiments.sh 2>/dev/null || echo "")

if [ -n "$CONFIG_SECTION" ]; then
    if echo "$CONFIG_SECTION" | grep -q "ASYNC_PROFILER_LIB"; then
        log_success "Agent库路径配置在配置区域中"
    else
        log_warn "Agent库路径配置不在配置区域中"
    fi
    
    if echo "$CONFIG_SECTION" | grep -q "ENABLE_CPU_PROFILING"; then
        log_success "CPU分析配置存在"
    else
        log_error "CPU分析配置缺失"
    fi
    
    if echo "$CONFIG_SECTION" | grep -q "ENABLE_ALLOC_PROFILING"; then
        log_success "内存分析配置存在"
    else
        log_error "内存分析配置缺失"
    fi
else
    log_warn "无法找到配置区域"
fi

# 7. 检查JVM参数构建
log_info "检查JVM参数构建..."

if grep -q "agentpath.*ASYNC_PROFILER_LIB" run_experiments.sh; then
    log_success "JVM Agent参数构建正确"
else
    log_error "JVM Agent参数构建有问题"
fi

# 8. 检查进程管理逻辑
log_info "检查进程管理逻辑..."

if grep -q "跟随应用程序完整生命周期" run_experiments.sh; then
    log_success "Agent模式说明存在"
else
    log_warn "Agent模式说明可能缺失"
fi

# 检查是否移除了旧的attach模式CPU分析代码
if grep -q "CPU_ASPROF_CMD" run_experiments.sh; then
    log_warn "发现旧的CPU attach模式代码，可能需要清理"
else
    log_success "旧的CPU attach模式代码已清理"
fi

# 9. 模拟配置测试
log_info "模拟配置测试..."

# 模拟配置变量
ENABLE_PROFILING=true
ENABLE_CPU_PROFILING=true
ENABLE_ALLOC_PROFILING=true
CPU_PROFILING_EVENT="cpu"
PROFILING_INTERVAL="10ms"
PLI_MODE="original"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 模拟文件名生成
CPU_FLAME_GRAPH_FILE="cpu_flamegraph_${PLI_MODE}_${TIMESTAMP}.html"
ALLOC_JFR_FILE="alloc_profiling_${PLI_MODE}_${TIMESTAMP}.jfr"

# 模拟Agent参数构建
if [ "$ENABLE_CPU_PROFILING" = "true" ]; then
    CPU_AGENT_PARAMS="start,event=${CPU_PROFILING_EVENT},interval=${PROFILING_INTERVAL},file=${CPU_FLAME_GRAPH_FILE}"
    AGENT_PARAMS="$CPU_AGENT_PARAMS"
    log_success "Agent参数构建成功: $AGENT_PARAMS"
else
    log_info "CPU分析未启用"
fi

# 10. 生成测试报告
echo
echo "============================================================"
echo "                验证结果总结"
echo "============================================================"

echo "Agent模式配置检查:"
echo "✅ Agent库文件: $ASYNC_PROFILER_LIB"
echo "✅ Agent模式测试: 通过"
echo "✅ JVM参数配置: 正确"

echo
echo "功能配置检查:"
echo "✅ CPU分析: Agent模式"
echo "✅ 内存分析: Attach模式"
echo "✅ 配置集中化: 保持"

echo
echo "文件生成测试:"
echo "📄 CPU火焰图: $CPU_FLAME_GRAPH_FILE"
echo "📄 内存JFR文件: $ALLOC_JFR_FILE"

echo
echo "============================================================"
echo "                使用建议"
echo "============================================================"

log_info "Agent模式已正确配置，建议测试步骤:"
echo "1. 运行快速测试:"
echo "   DATASET_PATH='data/airport.csv' ALGORITHM_TIMEOUT='5' ./run_experiments.sh"
echo
echo "2. 检查生成的文件:"
echo "   ls -la cpu_flamegraph_*.html"
echo "   ls -la alloc_profiling_*.jfr"
echo
echo "3. 验证Agent模式特性:"
echo "   - CPU分析自动跟随应用程序生命周期"
echo "   - 无需手动管理profiler进程"
echo "   - 火焰图包含完整的执行过程"

echo
echo "4. 查看详细文档:"
echo "   cat ASYNC_PROFILER_AGENT_MODE_GUIDE.md"

echo
log_success "Agent模式验证完成！"
echo "============================================================"

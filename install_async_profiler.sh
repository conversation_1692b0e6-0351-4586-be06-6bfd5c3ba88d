#!/bin/bash

# Async Profiler v4.1 安装和验证脚本
# 专门为SSH服务器环境设计

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
ASYNC_PROFILER_VERSION="4.1"
INSTALL_DIR="/opt/async-profiler"
DOWNLOAD_URL="https://github.com/jvm-profiling-tools/async-profiler/releases/download/v${ASYNC_PROFILER_VERSION}/async-profiler-${ASYNC_PROFILER_VERSION}-linux-x64.tar.gz"
TEMP_DIR="/tmp"

echo "============================================================"
echo "        Async Profiler v${ASYNC_PROFILER_VERSION} 安装脚本"
echo "============================================================"

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    log_error "此脚本需要root权限运行"
    log_info "请使用: sudo $0"
    exit 1
fi

# 检查系统架构
ARCH=$(uname -m)
if [ "$ARCH" != "x86_64" ]; then
    log_error "不支持的系统架构: $ARCH"
    log_info "此脚本仅支持x86_64架构"
    exit 1
fi

# 检查是否已安装
if [ -f "$INSTALL_DIR/bin/asprof" ]; then
    log_warn "检测到已安装的async-profiler"
    
    # 检查版本
    CURRENT_VERSION=$("$INSTALL_DIR/bin/asprof" --version 2>/dev/null | head -n1 || echo "未知版本")
    log_info "当前版本: $CURRENT_VERSION"
    
    read -p "是否要重新安装? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "跳过安装，进行验证..."
        SKIP_INSTALL=true
    else
        SKIP_INSTALL=false
    fi
else
    SKIP_INSTALL=false
fi

if [ "$SKIP_INSTALL" = "false" ]; then
    # 步骤1: 下载async-profiler
    log_info "步骤1: 下载async-profiler v${ASYNC_PROFILER_VERSION}..."
    
    cd "$TEMP_DIR"
    ARCHIVE_FILE="async-profiler-${ASYNC_PROFILER_VERSION}-linux-x64.tar.gz"
    
    if [ -f "$ARCHIVE_FILE" ]; then
        log_info "发现已下载的文件，跳过下载"
    else
        log_info "从GitHub下载: $DOWNLOAD_URL"
        if ! wget -q --show-progress "$DOWNLOAD_URL"; then
            log_error "下载失败"
            log_info "请检查网络连接或手动下载文件"
            exit 1
        fi
    fi
    
    log_success "下载完成"
    
    # 步骤2: 创建安装目录
    log_info "步骤2: 创建安装目录..."
    
    if [ -d "$INSTALL_DIR" ]; then
        log_warn "安装目录已存在，将清理旧文件"
        rm -rf "$INSTALL_DIR"
    fi
    
    mkdir -p "$INSTALL_DIR"
    log_success "安装目录创建完成: $INSTALL_DIR"
    
    # 步骤3: 解压安装
    log_info "步骤3: 解压安装文件..."
    
    if ! tar -xzf "$ARCHIVE_FILE" -C "$INSTALL_DIR" --strip-components=1; then
        log_error "解压失败"
        exit 1
    fi
    
    log_success "解压完成"
    
    # 步骤4: 设置权限
    log_info "步骤4: 设置文件权限..."
    
    chmod +x "$INSTALL_DIR/bin/asprof"
    chown -R root:root "$INSTALL_DIR"
    
    log_success "权限设置完成"
    
    # 清理下载文件
    rm -f "$TEMP_DIR/$ARCHIVE_FILE"
    log_info "清理临时文件完成"
fi

# 步骤5: 配置内核参数
log_info "步骤5: 配置内核参数..."

# 检查当前内核参数
CURRENT_PERF=$(sysctl -n kernel.perf_event_paranoid 2>/dev/null || echo "未设置")
CURRENT_KPTR=$(sysctl -n kernel.kptr_restrict 2>/dev/null || echo "未设置")

log_info "当前内核参数:"
log_info "  kernel.perf_event_paranoid = $CURRENT_PERF"
log_info "  kernel.kptr_restrict = $CURRENT_KPTR"

# 设置临时参数
log_info "设置临时内核参数..."
sysctl kernel.perf_event_paranoid=1
sysctl kernel.kptr_restrict=0

# 询问是否永久设置
read -p "是否要永久设置内核参数? (推荐) (Y/n): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Nn]$ ]]; then
    log_info "设置永久内核参数..."
    
    # 备份原配置
    if [ -f /etc/sysctl.conf ]; then
        cp /etc/sysctl.conf /etc/sysctl.conf.backup.$(date +%Y%m%d_%H%M%S)
    fi
    
    # 添加或更新配置
    if ! grep -q "kernel.perf_event_paranoid" /etc/sysctl.conf; then
        echo "kernel.perf_event_paranoid=1" >> /etc/sysctl.conf
    else
        sed -i 's/^kernel.perf_event_paranoid=.*/kernel.perf_event_paranoid=1/' /etc/sysctl.conf
    fi
    
    if ! grep -q "kernel.kptr_restrict" /etc/sysctl.conf; then
        echo "kernel.kptr_restrict=0" >> /etc/sysctl.conf
    else
        sed -i 's/^kernel.kptr_restrict=.*/kernel.kptr_restrict=0/' /etc/sysctl.conf
    fi
    
    sysctl -p
    log_success "永久内核参数设置完成"
else
    log_warn "仅设置临时内核参数，重启后将失效"
fi

# 步骤6: 验证安装
log_info "步骤6: 验证安装..."

ASPROF_PATH="$INSTALL_DIR/bin/asprof"

# 检查文件存在性
if [ ! -f "$ASPROF_PATH" ]; then
    log_error "asprof可执行文件不存在: $ASPROF_PATH"
    exit 1
fi

# 检查执行权限
if [ ! -x "$ASPROF_PATH" ]; then
    log_error "asprof没有执行权限: $ASPROF_PATH"
    exit 1
fi

# 测试命令执行
log_info "测试asprof命令..."
if ! "$ASPROF_PATH" --help >/dev/null 2>&1; then
    log_error "asprof命令执行失败"
    exit 1
fi

# 显示版本信息
VERSION_INFO=$("$ASPROF_PATH" --version 2>/dev/null | head -n1 || echo "版本信息获取失败")
log_success "asprof命令测试通过"
log_info "版本信息: $VERSION_INFO"

# 步骤7: 创建符号链接（可选）
read -p "是否创建全局符号链接 /usr/local/bin/asprof? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    ln -sf "$ASPROF_PATH" /usr/local/bin/asprof
    log_success "符号链接创建完成: /usr/local/bin/asprof"
fi

echo
echo "============================================================"
echo "                安装完成"
echo "============================================================"

log_success "Async Profiler v${ASYNC_PROFILER_VERSION} 安装成功!"
echo
log_info "安装位置: $INSTALL_DIR"
log_info "可执行文件: $ASPROF_PATH"
log_info "版本信息: $VERSION_INFO"
echo
log_info "使用示例:"
log_info "  # 分析Java进程30秒并生成火焰图"
log_info "  $ASPROF_PATH -d 30 -f flamegraph.html <PID>"
echo
log_info "  # 查看帮助信息"
log_info "  $ASPROF_PATH --help"
echo
log_info "现在可以运行 run_experiments.sh 脚本进行性能分析了!"

echo "============================================================"

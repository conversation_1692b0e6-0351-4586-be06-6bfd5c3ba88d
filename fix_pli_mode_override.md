# PLI模式被覆盖问题的修复方案

## 问题分析

通过代码分析发现，在`ExperimentRunner.java`的`configureMemoryOptimization()`方法中，第620行有一个关键问题：

```java
System.setProperty("pli.force.streaming", "true"); // 强制使用流式处理
```

这个设置会覆盖之前在`configurePLIMode()`方法中设置的PLI模式配置，导致无论`--pli-mode`参数设置为什么值，都会强制使用流式处理。

## 修复方案

### 方案1：修改ExperimentRunner.java（推荐）

在`AFD-algorithms/experiment/src/main/java/experiment/ExperimentRunner.java`文件中进行以下修改：

#### 修改1：更新configureMemoryOptimization方法

找到`configureMemoryOptimization()`方法中的第620行左右：

```java
// 原代码（有问题）
System.setProperty("pli.force.streaming", "true"); // 强制使用流式处理
```

替换为：

```java
// 修复后的代码
// 只有在dynamic模式下才启用流式处理
if (PLI_MODE == PLIMode.DYNAMIC) {
    System.setProperty("pli.force.streaming", "true");
    log_info("PLI模式: DYNAMIC - 启用流式处理");
} else {
    System.setProperty("pli.force.streaming", "false");
    log_info("PLI模式: " + PLI_MODE + " - 禁用流式处理");
}
```

#### 修改2：确保PLI模式配置的优先级

在`configurePLIMode()`方法的末尾添加：

```java
// 在configurePLIMode()方法末尾添加
log_info("PLI模式配置完成，当前模式: " + PLI_MODE);
log_info("关键系统属性状态:");
log_info("  pli.force.original.only = " + System.getProperty("pli.force.original.only"));
log_info("  pli.disable.optimization = " + System.getProperty("pli.disable.optimization"));
log_info("  pli.disable.dynamic.switching = " + System.getProperty("pli.disable.dynamic.switching"));
```

#### 修改3：在main方法中调整调用顺序

确保`configurePLIMode()`在`configureMemoryOptimization()`之后调用：

```java
// 在main方法中
parseArgs(args);
configureMemoryOptimization(); // 先配置内存
configurePLIMode();            // 后配置PLI模式，确保不被覆盖
```

### 方案2：通过JVM系统属性强制覆盖（已在run_experiments.sh中实现）

在`run_experiments.sh`中，我们已经添加了强制的JVM系统属性：

```bash
if [ "$PLI_MODE" = "original" ]; then
    JVM_ARGS+=(
        "-Dpli.force.original.only=true"
        "-Dpli.disable.optimization=true"
        "-Dpli.disable.dynamic.switching=true"
        "-Dpli.force.streaming=false"           # 关键：强制禁用流式处理
        "-Dpli.enable.optimization.integrator=false"
    )
fi
```

这些JVM参数会在程序启动时设置，优先级高于程序内部的`System.setProperty()`调用。

## 验证修复效果

### 1. 检查日志输出

运行实验时，查看日志中的PLI模式配置信息：

```bash
# 启用调试模式
DEBUG_MODE=true ./run_experiments.sh

# 查找PLI相关日志
grep -i "pli" gc-128gb-*.log
```

### 2. 验证系统属性

在ExperimentRunner中添加调试输出：

```java
// 在实验开始前添加
System.out.println("=== PLI模式验证 ===");
System.out.println("PLI_MODE: " + PLI_MODE);
System.out.println("pli.force.original.only: " + System.getProperty("pli.force.original.only"));
System.out.println("pli.force.streaming: " + System.getProperty("pli.force.streaming"));
System.out.println("pli.disable.optimization: " + System.getProperty("pli.disable.optimization"));
```

### 3. 观察PLI行为

- **原始模式**：应该看到传统的PLI缓存操作
- **动态模式**：应该看到流式处理和优化切换

## 临时解决方案

如果无法修改Java代码，可以通过以下方式临时解决：

### 1. 强制JVM参数

在run_experiments.sh中添加更强的覆盖：

```bash
# 在JVM_ARGS中添加（已实现）
"-Dpli.force.streaming=false"
"-Dpli.force.original.only=true"
```

### 2. 环境变量

设置环境变量来影响PLI行为：

```bash
export PLI_FORCE_ORIGINAL=true
export PLI_DISABLE_STREAMING=true
```

### 3. 配置文件

创建PLI配置文件：

```bash
# 创建pli.properties
echo "pli.force.original.only=true" > pli.properties
echo "pli.force.streaming=false" >> pli.properties

# 在JVM参数中引用
JVM_ARGS+=("-Dpli.config.file=pli.properties")
```

## 测试步骤

### 1. 快速测试

```bash
# 使用小数据集测试
DATASET_PATH='data/airport.csv' \
ALGORITHM_TIMEOUT='2' \
PLI_MODE='original' \
DEBUG_MODE=true \
./run_experiments.sh
```

### 2. 检查输出

```bash
# 查看PLI模式相关日志
grep -i "pli\|streaming\|original" gc-128gb-*.log

# 查看实验结果
tail -n 20 result/result0807_test.csv
```

### 3. 验证行为

观察实验过程中的内存使用模式：
- **原始模式**：内存使用相对稳定，PLI缓存增长
- **流式模式**：内存使用波动，定期清理

## 预期效果

修复后，应该看到：

1. **日志输出**：
   ```
   PLI模式配置完成，当前模式: ORIGINAL
   pli.force.original.only = true
   pli.force.streaming = false
   ```

2. **行为变化**：
   - 使用传统PLI缓存而不是流式处理
   - 内存使用模式符合原始PLI实现
   - 不会出现动态切换日志

3. **性能特征**：
   - 内存使用更稳定
   - PLI缓存命中率更高
   - 符合原始实现的性能特征

## 注意事项

1. **重新编译**：修改Java代码后需要重新编译
2. **测试验证**：使用小数据集先验证修复效果
3. **日志监控**：密切关注PLI相关的日志输出
4. **性能对比**：对比修复前后的性能差异

通过这些修复方案，应该能够解决PLI模式被覆盖的问题，确保`--pli-mode original`参数能够正确生效。

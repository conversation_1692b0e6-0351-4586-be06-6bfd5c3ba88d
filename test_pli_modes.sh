#!/bin/bash

# PLI模式测试脚本
# 用于验证PLI算法实现切换功能

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查JAR文件
JAR_FILE="AFD-algorithms/experiment/target/experiment-1.0-SNAPSHOT-jar-with-dependencies.jar"
MAIN_CLASS="experiment.ExperimentRunner"

if [ ! -f "$JAR_FILE" ]; then
    log_error "JAR文件不存在: $JAR_FILE"
    log_info "请先编译项目: mvn clean package -DskipTests"
    exit 1
fi

# 测试数据集（使用小数据集进行快速测试）
TEST_DATASET="data/airport.csv"
if [ ! -f "$TEST_DATASET" ]; then
    log_warn "测试数据集不存在: $TEST_DATASET"
    log_info "将使用默认数据集路径进行测试"
    TEST_DATASET="data"
fi

# 创建测试结果目录
mkdir -p test_results

echo "============================================================"
echo "                PLI模式功能测试"
echo "============================================================"

# 测试1: 原始模式
log_info "测试1: 原始PLI模式"
echo "命令: java -cp $JAR_FILE $MAIN_CLASS --dataset $TEST_DATASET --pli-mode original --results-file test_results/test_original.csv --timeout 5 --sampling-mode NO_SAMPLING --run-tane false"

java -cp "$JAR_FILE" "$MAIN_CLASS" \
    --dataset "$TEST_DATASET" \
    --pli-mode original \
    --results-file "test_results/test_original.csv" \
    --timeout 5 \
    --sampling-mode NO_SAMPLING \
    --run-tane false \
    --max-error 0.1 \
    --sample-param 100 || {
    log_error "原始模式测试失败"
    exit 1
}

log_success "原始模式测试完成"
echo

# 测试2: 动态模式
log_info "测试2: 动态PLI模式"
echo "命令: java -cp $JAR_FILE $MAIN_CLASS --dataset $TEST_DATASET --pli-mode dynamic --results-file test_results/test_dynamic.csv --timeout 5 --sampling-mode NO_SAMPLING --run-tane false"

java -cp "$JAR_FILE" "$MAIN_CLASS" \
    --dataset "$TEST_DATASET" \
    --pli-mode dynamic \
    --results-file "test_results/test_dynamic.csv" \
    --timeout 5 \
    --sampling-mode NO_SAMPLING \
    --run-tane false \
    --max-error 0.1 \
    --sample-param 100 || {
    log_error "动态模式测试失败"
    exit 1
}

log_success "动态模式测试完成"
echo

# 测试3: 短参数形式
log_info "测试3: 短参数形式 (-p)"
echo "命令: java -cp $JAR_FILE $MAIN_CLASS --dataset $TEST_DATASET -p original --results-file test_results/test_short_param.csv --timeout 5 --sampling-mode NO_SAMPLING --run-tane false"

java -cp "$JAR_FILE" "$MAIN_CLASS" \
    --dataset "$TEST_DATASET" \
    -p original \
    --results-file "test_results/test_short_param.csv" \
    --timeout 5 \
    --sampling-mode NO_SAMPLING \
    --run-tane false \
    --max-error 0.1 \
    --sample-param 100 || {
    log_error "短参数形式测试失败"
    exit 1
}

log_success "短参数形式测试完成"
echo

# 测试4: 无效参数处理
log_info "测试4: 无效PLI模式参数处理"
echo "命令: java -cp $JAR_FILE $MAIN_CLASS --dataset $TEST_DATASET --pli-mode invalid --results-file test_results/test_invalid.csv --timeout 5 --sampling-mode NO_SAMPLING --run-tane false"

java -cp "$JAR_FILE" "$MAIN_CLASS" \
    --dataset "$TEST_DATASET" \
    --pli-mode invalid \
    --results-file "test_results/test_invalid.csv" \
    --timeout 5 \
    --sampling-mode NO_SAMPLING \
    --run-tane false \
    --max-error 0.1 \
    --sample-param 100 || {
    log_warn "无效参数测试按预期失败（这是正常的）"
}

log_success "无效参数处理测试完成"
echo

# 检查结果文件
echo "============================================================"
echo "                测试结果检查"
echo "============================================================"

for result_file in test_results/test_*.csv; do
    if [ -f "$result_file" ]; then
        log_success "结果文件生成: $result_file"
        log_info "文件大小: $(du -h "$result_file" | cut -f1)"
        log_info "行数: $(wc -l < "$result_file")"
    else
        log_warn "结果文件未生成: $result_file"
    fi
done

echo
echo "============================================================"
echo "                测试总结"
echo "============================================================"

log_success "PLI模式功能测试完成！"
log_info "测试结果保存在 test_results/ 目录中"
log_info "可以查看结果文件验证不同PLI模式的性能差异"

echo
log_info "下一步建议："
log_info "1. 查看测试结果文件中的PLI统计信息"
log_info "2. 使用完整的run_experiments.sh脚本进行正式实验"
log_info "3. 启用性能分析功能进行深入分析"

echo
echo "============================================================"

#!/bin/bash

# AFD-measures 128GB服务器优化实验启动脚本
# 此脚本是原始run_experiments.sh的优化版本，专门针对128GB大内存服务器

# 如果任何命令执行失败，立即退出脚本
set -e

# ============================================================
#                    配置区域 - 所有可修改的参数
# ============================================================

# --- 实验基础配置 ---
DATASET_PATH='data/int/EQ-500K-12.csv'     # 数据集路径
RESULTS_FILE='result/result0807_test.csv'  # 结果文件路径

# --- Java应用程序配置 ---
JAR_FILE="AFD-algorithms/experiment/target/experiment-1.0-SNAPSHOT-jar-with-dependencies.jar"
MAIN_CLASS="experiment.ExperimentRunner"

# --- 实验参数配置 ---
PLI_MODE='original'                        # PLI模式: original 或 dynamic
SAMPLING_MODE="ALL"                        # 采样模式: ALL, NO_SAMPLING, RANDOM, FOCUSED, NEYMAN
RUN_MODE="APPEND"                          # 运行模式: APPEND 或 OVERWRITE
RUN_TANE="true"                           # 是否运行TANE算法
ALGORITHM_TIMEOUT="120"                    # 算法超时时间（分钟）
MAX_ERROR="0.05"                          # 最大错误率
SAMPLE_PARAM="200"                        # 采样参数
RANDOM_SEED="114514"                      # 随机种子

# --- 内存配置 - 64GB堆内存保守配置 ---
HEAP_SIZE="64g"                           # 64GB堆内存（保守配置，确保可靠性）
NEW_SIZE="16g"                            # 16GB新生代（约25%）
METASPACE_SIZE="3g"                       # 3GB元空间
REGION_SIZE="32m"                         # 32MB G1区域大小（适合64GB堆）
PAUSE_TARGET="150"                        # 150ms GC暂停目标

# --- PLI缓存配置 - 保守配置确保稳定性 ---
PLI_CACHE_MB=24576                        # 24GB PLI缓存（利用堆外内存）
PLI_CLEANUP_MB=19660                      # 20GB 清理阈值（80%）

# --- 内存安全配置 ---
MEMORY_SAFETY_MARGIN_GB=16                # 为系统预留16GB内存
ENABLE_MEMORY_VALIDATION=true             # 启用内存验证检查
FALLBACK_HEAP_SIZE="48g"                  # 备用堆大小（如果64GB失败）
FALLBACK_PLI_CACHE_MB=16384               # 备用PLI缓存（16GB）

# --- G1GC详细配置 - 针对64GB堆优化 ---
G1_NEW_SIZE_PERCENT=25                    # 新生代占比
G1_MAX_NEW_SIZE_PERCENT=35                # 最大新生代占比
G1_MIXED_GC_COUNT_TARGET=8                # 混合GC目标次数
G1_MIXED_GC_LIVE_THRESHOLD=85             # 混合GC存活阈值
G1_OLD_CSET_REGION_THRESHOLD=10           # 老年代收集集合区域阈值
G1_RESERVE_PERCENT=10                     # G1保留百分比（减少以节省内存）
G1_HEAP_WASTE_PERCENT=3                   # 堆浪费百分比（减少以提高效率）

# --- 大页配置 ---
ENABLE_LARGE_PAGES=true                   # 是否启用大页（可配置）
LARGE_PAGE_SIZE="2m"                      # 大页大小
DISABLE_LARGE_PAGES_ON_FAILURE=true       # 大页失败时自动禁用

# --- 性能分析配置 ---
ENABLE_PROFILING=true                     # 是否启用性能分析
PROFILING_DURATION=0                      # 0表示跟随程序运行时间
PROFILING_INTERVAL=10ms                   # 采样间隔
ASYNC_PROFILER_PATH="/opt/async-profiler" # async-profiler安装路径

# --- 性能分析详细配置 (async-profiler v4.1兼容) ---
ENABLE_CPU_PROFILING=true                 # 启用CPU分析 (生成HTML火焰图)
ENABLE_ALLOC_PROFILING=true               # 启用内存分配分析 (生成JFR文件)
CPU_PROFILING_EVENT="cpu"                 # CPU分析事件类型
ALLOC_PROFILING_EVENT="alloc"             # 内存分配分析事件类型
HTML_OUTPUT_FORMAT="flamegraph"           # HTML输出格式

# --- 日志和调试配置 ---
GC_LOG_PREFIX="gc-128gb"                  # GC日志文件前缀
GC_LOG_FILE_COUNT=10                      # GC日志文件数量
GC_LOG_FILE_SIZE="100M"                   # GC日志文件大小
DEBUG_MODE="${DEBUG:-false}"              # 调试模式

# ============================================================
#                    脚本功能区域 - 请勿修改
# ============================================================

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 内存验证和配置调整函数
validate_and_adjust_memory_config() {
    log_info "验证内存配置..."

    # 获取系统内存信息
    local total_memory_kb=$(grep MemTotal /proc/meminfo | awk '{print $2}')
    local available_memory_kb=$(grep MemAvailable /proc/meminfo | awk '{print $2}' 2>/dev/null || echo "0")
    local free_memory_kb=$(grep MemFree /proc/meminfo | awk '{print $2}')

    # 如果MemAvailable不可用，使用MemFree作为估算
    if [ "$available_memory_kb" -eq 0 ]; then
        available_memory_kb=$free_memory_kb
    fi

    local total_memory_gb=$((total_memory_kb / 1024 / 1024))
    local available_memory_gb=$((available_memory_kb / 1024 / 1024))

    log_info "系统内存状态:"
    log_info "  总内存: ${total_memory_gb}GB"
    log_info "  可用内存: ${available_memory_gb}GB"

    # 计算当前配置的总内存需求
    local heap_size_gb=${HEAP_SIZE%g}
    local pli_cache_gb=$((PLI_CACHE_MB / 1024))
    local total_required_gb=$((heap_size_gb + pli_cache_gb + MEMORY_SAFETY_MARGIN_GB))

    log_info "当前配置内存需求:"
    log_info "  堆内存: ${heap_size_gb}GB"
    log_info "  PLI缓存: ${pli_cache_gb}GB"
    log_info "  系统预留: ${MEMORY_SAFETY_MARGIN_GB}GB"
    log_info "  总需求: ${total_required_gb}GB"

    # 检查内存是否足够
    if [ $total_required_gb -gt $available_memory_gb ]; then
        log_warn "内存配置可能过高，尝试调整到备用配置..."

        # 使用备用配置
        HEAP_SIZE="$FALLBACK_HEAP_SIZE"
        PLI_CACHE_MB=$FALLBACK_PLI_CACHE_MB
        NEW_SIZE="12g"  # 调整新生代为25%
        PLI_CLEANUP_MB=$((FALLBACK_PLI_CACHE_MB * 80 / 100))  # 80%清理阈值

        local fallback_heap_gb=${FALLBACK_HEAP_SIZE%g}
        local fallback_pli_gb=$((FALLBACK_PLI_CACHE_MB / 1024))
        local fallback_total_gb=$((fallback_heap_gb + fallback_pli_gb + MEMORY_SAFETY_MARGIN_GB))

        log_info "调整后的内存配置:"
        log_info "  堆内存: ${fallback_heap_gb}GB"
        log_info "  PLI缓存: ${fallback_pli_gb}GB"
        log_info "  总需求: ${fallback_total_gb}GB"

        if [ $fallback_total_gb -gt $available_memory_gb ]; then
            log_error "即使使用备用配置，内存仍然不足"
            log_error "建议:"
            log_error "1. 释放系统内存（关闭不必要的服务）"
            log_error "2. 手动调整脚本中的HEAP_SIZE和PLI_CACHE_MB"
            log_error "3. 增加系统交换空间"
            return 1
        fi
    fi

    # 检查大页配置
    if [ "$ENABLE_LARGE_PAGES" = "true" ]; then
        local hugepages_total=$(cat /proc/meminfo | grep HugePages_Total | awk '{print $2}' 2>/dev/null || echo "0")
        local hugepages_size_kb=$(cat /proc/meminfo | grep Hugepagesize | awk '{print $2}' 2>/dev/null || echo "2048")
        local hugepages_available_gb=$(echo "scale=1; $hugepages_total * $hugepages_size_kb / 1024 / 1024" | bc 2>/dev/null || echo "0")

        log_info "大页配置检查:"
        log_info "  大页总数: $hugepages_total"
        log_info "  大页大小: ${hugepages_size_kb}KB"
        log_info "  大页总容量: ${hugepages_available_gb}GB"

        if [ "$hugepages_total" -eq 0 ] || [ "$(echo "$hugepages_available_gb < $heap_size_gb" | bc 2>/dev/null || echo "1")" -eq 1 ]; then
            if [ "$DISABLE_LARGE_PAGES_ON_FAILURE" = "true" ]; then
                log_warn "大页配置不足，自动禁用大页支持"
                ENABLE_LARGE_PAGES=false
            else
                log_warn "大页配置可能不足，但仍将尝试启用"
            fi
        fi
    fi

    log_success "内存配置验证完成"
    return 0
}

# 检查async profiler是否可用
check_async_profiler() {
    if [ "$ENABLE_PROFILING" = "true" ]; then
        if [ ! -d "$ASYNC_PROFILER_PATH" ]; then
            log_warn "Async Profiler路径不存在: $ASYNC_PROFILER_PATH"
            log_info "请下载并安装async-profiler v4.1:"
            log_info "1. 访问: https://github.com/jvm-profiling-tools/async-profiler/releases"
            log_info "2. 下载最新版本的async-profiler-4.1-linux-x64.tar.gz"
            log_info "3. 解压到 $ASYNC_PROFILER_PATH"
            log_info "4. 确保bin/asprof有执行权限"
            log_warn "禁用性能分析功能"
            ENABLE_PROFILING=false
            return 1
        fi

        PROFILER_EXECUTABLE="$ASYNC_PROFILER_PATH/bin/asprof"
        if [ ! -f "$PROFILER_EXECUTABLE" ] || [ ! -x "$PROFILER_EXECUTABLE" ]; then
            log_error "Async Profiler可执行文件不存在或无执行权限: $PROFILER_EXECUTABLE"
            log_info "请检查async-profiler v4.1是否正确安装"
            log_info "预期文件位置: $PROFILER_EXECUTABLE"
            log_warn "禁用性能分析功能"
            ENABLE_PROFILING=false
            return 1
        fi

        # 测试asprof命令是否可用
        if ! "$PROFILER_EXECUTABLE" --help >/dev/null 2>&1; then
            log_error "Async Profiler可执行文件无法正常运行: $PROFILER_EXECUTABLE"
            log_warn "禁用性能分析功能"
            ENABLE_PROFILING=false
            return 1
        fi

        log_success "Async Profiler v4.1检查通过: $PROFILER_EXECUTABLE"
        return 0
    fi
    return 0
}

# --- 1. 内存配置验证和调整 ---
if [ "$ENABLE_MEMORY_VALIDATION" = "true" ]; then
    if ! validate_and_adjust_memory_config; then
        log_error "内存配置验证失败，无法继续执行"
        exit 1
    fi
fi

# --- 2. 检查async profiler ---
check_async_profiler

# --- 3. 项目编译 ---
log_info "正在使用Maven编译项目..."
mvn clean package -DskipTests
log_success "项目编译完成!"

# --- 3. 实验配置 ---
# 检查JAR文件是否存在
if [ ! -f "$JAR_FILE" ]; then
    log_warn "未找到JAR文件: $JAR_FILE"
    log_info "尝试使用优化启动脚本构建..."
    cd AFD-algorithms/experiment
    if [ -f "run-optimized-experiment.sh" ]; then
        chmod +x run-optimized-experiment.sh
        ./run-optimized-experiment.sh --dry-run
        cd ../..
    else
        echo "错误: 无法找到实验JAR文件"
        exit 1
    fi
fi

# --- 4. 128GB服务器内存配置 ---
log_info "配置128GB服务器专用JVM参数..."

# 检测系统内存
TOTAL_MEMORY_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
TOTAL_MEMORY_MB=$((TOTAL_MEMORY_KB / 1024))
log_info "检测到系统内存: ${TOTAL_MEMORY_MB}MB ($(echo "scale=1; ${TOTAL_MEMORY_MB}/1024" | bc)GB)"

# 检测大页面支持
HUGEPAGES_TOTAL=$(cat /proc/meminfo | grep HugePages_Total | awk '{print $2}')
HUGEPAGES_SIZE_KB=$(cat /proc/meminfo | grep Hugepagesize | awk '{print $2}')
if [ "$HUGEPAGES_TOTAL" -gt 0 ]; then
    HUGEPAGES_AVAILABLE_GB=$(echo "scale=1; $HUGEPAGES_TOTAL * $HUGEPAGES_SIZE_KB / 1024 / 1024" | bc)
    log_info "检测到大页面支持: ${HUGEPAGES_TOTAL}个 × ${HUGEPAGES_SIZE_KB}KB = ${HUGEPAGES_AVAILABLE_GB}GB"
    USE_LARGE_PAGES=true
else
    log_warn "未检测到大页面支持，建议配置大页面以提升性能"
    USE_LARGE_PAGES=false
fi

# 构建JVM参数 - 基于动态调整后的配置
JVM_ARGS=(
    # 内存配置 - 动态调整的堆内存配置
    "-Xms${HEAP_SIZE}"
    "-Xmx${HEAP_SIZE}"
    "-XX:NewSize=${NEW_SIZE}"
    "-XX:MaxNewSize=${NEW_SIZE}"
    "-XX:MetaspaceSize=${METASPACE_SIZE}"
    "-XX:MaxMetaspaceSize=${METASPACE_SIZE}"

    # 性能优化
    "-XX:+UnlockExperimentalVMOptions"
    "-XX:+UseStringDeduplication"
    "-XX:+OptimizeStringConcat"
)

# 根据堆大小决定是否使用压缩指针
HEAP_SIZE_GB=${HEAP_SIZE%g}
if [ "$HEAP_SIZE_GB" -le 32 ]; then
    JVM_ARGS+=(
        "-XX:+UseCompressedOops"
        "-XX:+UseCompressedClassPointers"
    )
    log_info "启用压缩指针 (堆内存 <= 32GB)"
else
    JVM_ARGS+=(
        "-XX:-UseCompressedOops"
        "-XX:-UseCompressedClassPointers"
    )
    log_info "禁用压缩指针 (堆内存 > 32GB)"
fi

# G1GC配置
JVM_ARGS+=(
    "-XX:+UseG1GC"
    "-XX:MaxGCPauseMillis=${PAUSE_TARGET}"
    "-XX:G1HeapRegionSize=${REGION_SIZE}"
    "-XX:G1NewSizePercent=${G1_NEW_SIZE_PERCENT}"
    "-XX:G1MaxNewSizePercent=${G1_MAX_NEW_SIZE_PERCENT}"
    "-XX:G1MixedGCCountTarget=${G1_MIXED_GC_COUNT_TARGET}"
    "-XX:G1MixedGCLiveThresholdPercent=${G1_MIXED_GC_LIVE_THRESHOLD}"
    "-XX:G1OldCSetRegionThresholdPercent=${G1_OLD_CSET_REGION_THRESHOLD}"
    "-XX:G1ReservePercent=${G1_RESERVE_PERCENT}"

    # G1GC额外优化
    "-XX:+G1UseAdaptiveIHOP"
    "-XX:G1HeapWastePercent=${G1_HEAP_WASTE_PERCENT}"
    "-XX:G1MixedGCLiveThresholdPercent=${G1_MIXED_GC_LIVE_THRESHOLD}"

    # 监控和调试
    "-Xlog:gc*=info:file=${GC_LOG_PREFIX}-$(date +%Y%m%d-%H%M%S).log:time,uptime,level,tags:filecount=${GC_LOG_FILE_COUNT},filesize=${GC_LOG_FILE_SIZE}"
)

# 大页配置
if [ "$ENABLE_LARGE_PAGES" = "true" ]; then
    JVM_ARGS+=(
        "-XX:+UseLargePages"
        "-XX:LargePageSizeInBytes=${LARGE_PAGE_SIZE/m/M}"
    )
    log_info "启用大页支持 (${LARGE_PAGE_SIZE})"
else
    JVM_ARGS+=(
        "-XX:-UseLargePages"
    )
    log_info "禁用大页支持"
fi

# PLI相关的JVM参数
JVM_ARGS+=(
    # PLI缓存配置
    "-Dpli.cache.max.size.mb=${PLI_CACHE_MB}"
    "-Dpli.cache.cleanup.threshold.mb=${PLI_CLEANUP_MB}"

    # 内存管理优化
    "-XX:+UseG1GC"
    "-XX:+UnlockExperimentalVMOptions"
    "-XX:+UseTransparentHugePages"
    
    # PLI优化配置
    "-Dpli.cache.max.memory.mb=${PLI_CACHE_MB}"
    "-Dpli.cache.cleanup.threshold.mb=${PLI_CLEANUP_MB}"
    "-Dmemory.monitor.enabled=true"
    "-Dmemory.monitor.warning.threshold=0.8"
    "-Dmemory.monitor.critical.threshold=0.9"
    "-Dpli.performance.stats=true"
    "-Dpli.memory.stats=true"
    "-Dpli.enable.all.optimizations=true"
    "-Dstreaming.pli.chunk.size=300000"
    "-Dpli.cache.aggressive.mode=true"
    "-Dpli.cache.use.offheap=true"
)

# 根据大页面支持情况添加相应参数
if [ "$USE_LARGE_PAGES" = "true" ]; then
    JVM_ARGS+=(
        "-XX:+UseLargePages"
        "-XX:LargePageSizeInBytes=2m"
        "-XX:+AlwaysPreTouch"
    )
    log_info "启用大页面支持"
else
    # 不使用大页面时的优化
    JVM_ARGS+=(
        "-XX:+AlwaysPreTouch"
    )
    log_warn "未启用大页面，建议配置大页面以获得更好性能"
fi

# --- 5. 显示配置信息 ---
echo
echo "============================================================"
echo "        128GB服务器优化实验配置 (保守内存配置)"
echo "============================================================"
echo "系统内存:     ${TOTAL_MEMORY_MB}MB ($(echo "scale=1; ${TOTAL_MEMORY_MB}/1024" | bc)GB)"
echo "堆内存大小:   ${HEAP_SIZE} (保守配置确保稳定性)"
echo "新生代大小:   ${NEW_SIZE}"
echo "元空间大小:   ${METASPACE_SIZE}"
echo "G1区域大小:   ${REGION_SIZE}"
echo "GC暂停目标:   ${PAUSE_TARGET}ms"
echo "PLI缓存限制:  ${PLI_CACHE_MB}MB ($(echo "scale=1; ${PLI_CACHE_MB}/1024" | bc)GB)"
echo "PLI清理阈值:  ${PLI_CLEANUP_MB}MB ($(echo "scale=1; ${PLI_CLEANUP_MB}/1024" | bc)GB)"
echo "内存安全边距: ${MEMORY_SAFETY_MARGIN_GB}GB"
echo "PLI实现模式:  ${PLI_MODE}"
echo "数据集路径:   ${DATASET_PATH}"
echo "结果文件:     ${RESULTS_FILE}"
echo "采样模式:     ${SAMPLING_MODE}"
echo "算法超时:     ${ALGORITHM_TIMEOUT}分钟"
if [ "$ENABLE_PROFILING" = "true" ]; then
    echo "性能分析:     已启用 (async-profiler v4.1)"
else
    echo "性能分析:     已禁用"
fi
if [ "$ENABLE_LARGE_PAGES" = "true" ]; then
    echo "大页面支持:   已启用"
else
    echo "大页面支持:   已禁用"
fi

# 显示压缩指针状态
HEAP_SIZE_GB=${HEAP_SIZE%g}
if [ "$HEAP_SIZE_GB" -le 32 ]; then
    echo "压缩指针:     已启用 (堆内存 <= 32GB)"
else
    echo "压缩指针:     已禁用 (堆内存 > 32GB)"
fi

echo "内存验证:     $([ "$ENABLE_MEMORY_VALIDATION" = "true" ] && echo "已启用" || echo "已禁用")"
echo "============================================================"

# --- 6. 准备性能分析 ---
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
CPU_FLAME_GRAPH_FILE="cpu_flamegraph_${PLI_MODE}_${TIMESTAMP}.html"
ALLOC_JFR_FILE="alloc_profiling_${PLI_MODE}_${TIMESTAMP}.jfr"

if [ "$ENABLE_PROFILING" = "true" ]; then
    log_info "准备性能分析文件 (async-profiler v4.1兼容模式):"
    if [ "$ENABLE_CPU_PROFILING" = "true" ]; then
        log_info "- CPU火焰图文件: $CPU_FLAME_GRAPH_FILE"
    fi
    if [ "$ENABLE_ALLOC_PROFILING" = "true" ]; then
        log_info "- 内存分配JFR文件: $ALLOC_JFR_FILE"
    fi
fi

# --- 7. 执行实验 ---
echo
log_info "开始执行128GB服务器优化实验 (保守内存配置)..."
echo ">>  数据集路径: ${DATASET_PATH}"
echo ">>  PLI模式: ${PLI_MODE}"
echo ">>  采样模式: ${SAMPLING_MODE}"
echo ">>  运行TANE: ${RUN_TANE}"
echo ">>  结果文件: ${RESULTS_FILE} (${RUN_MODE}模式)"
echo ">>  超时时间: ${ALGORITHM_TIMEOUT}分钟"
echo ">>  内存配置: ${HEAP_SIZE}堆内存 + $(echo "scale=1; ${PLI_CACHE_MB}/1024" | bc)GB PLI缓存"
echo ">>  GC配置: G1GC, ${PAUSE_TARGET}ms暂停目标, ${REGION_SIZE}区域大小"
if [ "$ENABLE_PROFILING" = "true" ]; then
    echo ">>  性能分析: 已启用 (async-profiler v4.1)"
fi
echo "-------------------------------------------------"

# 构建完整的Java命令 - 使用配置区域的参数
JAVA_CMD=(
    "java"
    "${JVM_ARGS[@]}"
    "-cp" "$JAR_FILE"
    "$MAIN_CLASS"
    "--dataset" "${DATASET_PATH}"
    "--results-file" "${RESULTS_FILE}"
    "--pli-mode" "${PLI_MODE}"
    "--sampling-mode" "${SAMPLING_MODE}"
    "--run-mode" "${RUN_MODE}"
    "--run-tane" "${RUN_TANE}"
    "--timeout" "${ALGORITHM_TIMEOUT}"
    "--max-error" "${MAX_ERROR}"
    "--sample-param" "${SAMPLE_PARAM}"
    "--seed" "${RANDOM_SEED}"
)

# 显示完整命令（可选）
if [ "$DEBUG_MODE" = "true" ]; then
    log_info "执行命令:"
    printf '%s ' "${JAVA_CMD[@]}"
    echo
    echo
fi

# 启动性能分析（如果启用）
if [ "$ENABLE_PROFILING" = "true" ]; then
    log_info "启动async profiler v4.1 (兼容模式)..."

    # 启动Java程序并获取PID
    log_info "启动实验程序..."
    "${JAVA_CMD[@]}" &
    JAVA_PID=$!

    # 等待Java程序启动
    sleep 5

    # 检查Java程序是否还在运行
    if ! kill -0 $JAVA_PID 2>/dev/null; then
        log_error "Java程序启动失败"
        exit 1
    fi

    log_info "Java程序PID: $JAVA_PID"

    # 初始化profiler PID数组
    PROFILER_PIDS=()

    # 启动CPU分析 (生成HTML火焰图)
    if [ "$ENABLE_CPU_PROFILING" = "true" ]; then
        log_info "开始CPU性能分析 (生成HTML火焰图)..."

        # 构建CPU分析命令
        CPU_ASPROF_CMD=(
            "$PROFILER_EXECUTABLE"
            "-e" "$CPU_PROFILING_EVENT"
            "-i" "$PROFILING_INTERVAL"
            "-f" "$CPU_FLAME_GRAPH_FILE"
        )

        # 如果设置了持续时间，添加-d参数
        if [ "$PROFILING_DURATION" -gt 0 ]; then
            CPU_ASPROF_CMD+=("-d" "$PROFILING_DURATION")
        fi

        # 添加目标PID
        CPU_ASPROF_CMD+=("$JAVA_PID")

        # 启动CPU profiling
        "${CPU_ASPROF_CMD[@]}" &
        CPU_PROFILER_PID=$!
        PROFILER_PIDS+=($CPU_PROFILER_PID)

        log_info "CPU Profiler命令: ${CPU_ASPROF_CMD[*]}"
        log_info "CPU Profiler PID: $CPU_PROFILER_PID"
    fi

    # 启动内存分配分析 (生成JFR文件)
    if [ "$ENABLE_ALLOC_PROFILING" = "true" ]; then
        log_info "开始内存分配分析 (生成JFR文件)..."

        # 构建内存分配分析命令
        ALLOC_ASPROF_CMD=(
            "$PROFILER_EXECUTABLE"
            "-e" "$ALLOC_PROFILING_EVENT"
            "-i" "$PROFILING_INTERVAL"
            "-o" "jfr"
            "-f" "$ALLOC_JFR_FILE"
        )

        # 如果设置了持续时间，添加-d参数
        if [ "$PROFILING_DURATION" -gt 0 ]; then
            ALLOC_ASPROF_CMD+=("-d" "$PROFILING_DURATION")
        fi

        # 添加目标PID
        ALLOC_ASPROF_CMD+=("$JAVA_PID")

        # 启动内存分配profiling
        "${ALLOC_ASPROF_CMD[@]}" &
        ALLOC_PROFILER_PID=$!
        PROFILER_PIDS+=($ALLOC_PROFILER_PID)

        log_info "内存分配Profiler命令: ${ALLOC_ASPROF_CMD[*]}"
        log_info "内存分配Profiler PID: $ALLOC_PROFILER_PID"
    fi

    # 等待Java程序完成
    log_info "等待实验完成..."
    wait $JAVA_PID
    JAVA_EXIT_CODE=$?

    # 停止所有profiler进程
    if [ ${#PROFILER_PIDS[@]} -gt 0 ]; then
        log_info "停止性能分析进程..."

        for profiler_pid in "${PROFILER_PIDS[@]}"; do
            if kill -0 $profiler_pid 2>/dev/null; then
                log_info "停止profiler进程: $profiler_pid"
                # 对于async-profiler v4.1，发送SIGTERM信号来优雅停止
                kill -TERM $profiler_pid 2>/dev/null || true
            fi
        done

        # 等待一段时间让profiler完成文件写入
        sleep 3

        # 检查并强制终止仍在运行的profiler
        for profiler_pid in "${PROFILER_PIDS[@]}"; do
            if kill -0 $profiler_pid 2>/dev/null; then
                log_warn "强制终止profiler进程: $profiler_pid"
                kill -KILL $profiler_pid 2>/dev/null || true
            fi
            wait $profiler_pid 2>/dev/null || true
        done

        log_success "所有profiler进程已停止"
    fi

else
    # 不使用性能分析，直接执行
    log_info "启动实验..."
    "${JAVA_CMD[@]}"
    JAVA_EXIT_CODE=$?
fi

# 检查执行结果
if [ $JAVA_EXIT_CODE -eq 0 ]; then
    echo
    log_success "所有实验已成功完成!"
    log_success "你可以在 ${RESULTS_FILE} 文件中查看结果。"

    # 显示GC日志文件位置
    GC_LOG=$(ls -t gc-128gb-*.log 2>/dev/null | head -n1)
    if [ -n "$GC_LOG" ]; then
        log_info "GC日志文件: $GC_LOG"
    fi

    # 显示性能分析结果
    if [ "$ENABLE_PROFILING" = "true" ]; then
        echo
        log_success "性能分析完成! (async-profiler v4.1兼容模式)"

        # 显示CPU分析结果
        if [ "$ENABLE_CPU_PROFILING" = "true" ]; then
            if [ -f "$CPU_FLAME_GRAPH_FILE" ]; then
                log_info "CPU火焰图文件: $CPU_FLAME_GRAPH_FILE"
                log_info "查看CPU火焰图的方法:"
                log_info "1. 将文件下载到本地计算机"
                log_info "2. 使用浏览器打开HTML文件"
                log_info "3. 或者在服务器上使用: python3 -m http.server 8000"
                log_info "   然后在浏览器访问: http://服务器IP:8000/$CPU_FLAME_GRAPH_FILE"
            else
                log_warn "CPU火焰图文件未生成: $CPU_FLAME_GRAPH_FILE"
            fi
        fi

        # 显示内存分配分析结果
        if [ "$ENABLE_ALLOC_PROFILING" = "true" ]; then
            if [ -f "$ALLOC_JFR_FILE" ]; then
                log_info "内存分配JFR文件: $ALLOC_JFR_FILE"
                log_info "可以使用以下工具分析JFR文件:"
                log_info "- JProfiler: 专业的Java性能分析工具"
                log_info "- VisualVM: 免费的Java性能监控工具"
                log_info "- JDK Mission Control: Oracle官方JFR分析工具"
            else
                log_warn "内存分配JFR文件未生成: $ALLOC_JFR_FILE"
            fi
        fi

        echo
        log_info "性能分析说明:"
        log_info "- CPU分析: 显示方法调用的时间分布，适合识别CPU热点"
        log_info "- 内存分配分析: 显示内存分配模式，适合识别内存泄漏和优化点"
    fi

else
    echo
    log_error "实验执行失败，退出码: $JAVA_EXIT_CODE"
    log_warn "请检查日志以获取更多信息。"
    exit $JAVA_EXIT_CODE
fi
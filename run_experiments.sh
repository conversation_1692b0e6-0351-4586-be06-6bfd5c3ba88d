#!/bin/bash

# AFD-measures 128GB服务器优化实验启动脚本
# 此脚本是原始run_experiments.sh的优化版本，专门针对128GB大内存服务器

# 如果任何命令执行失败，立即退出脚本
set -e

# 文件名日期
DATE='0807'

# 数据集配置
DATASET_PATH='data/int/EQ-500K-12.csv'
RESULTS_FILE='result/result0807_test.csv'

# PLI模式配置 (original 或 dynamic)
PLI_MODE='original'

# Async Profiling配置
ENABLE_PROFILING=true
PROFILING_DURATION=0  # 0表示跟随程序运行时间
PROFILING_INTERVAL=10ms
ASYNC_PROFILER_PATH="/opt/async-profiler"  # 需要根据实际安装路径调整

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查async profiler是否可用
check_async_profiler() {
    if [ "$ENABLE_PROFILING" = "true" ]; then
        if [ ! -d "$ASYNC_PROFILER_PATH" ]; then
            log_warn "Async Profiler路径不存在: $ASYNC_PROFILER_PATH"
            log_info "请下载并安装async-profiler:"
            log_info "1. 访问: https://github.com/jvm-profiling-tools/async-profiler/releases"
            log_info "2. 下载最新版本的async-profiler-*-linux-x64.tar.gz"
            log_info "3. 解压到 $ASYNC_PROFILER_PATH"
            log_info "4. 确保profiler.sh有执行权限"
            log_warn "禁用性能分析功能"
            ENABLE_PROFILING=false
            return 1
        fi

        PROFILER_SCRIPT="$ASYNC_PROFILER_PATH/profiler.sh"
        if [ ! -f "$PROFILER_SCRIPT" ] || [ ! -x "$PROFILER_SCRIPT" ]; then
            log_error "Async Profiler脚本不存在或无执行权限: $PROFILER_SCRIPT"
            log_warn "禁用性能分析功能"
            ENABLE_PROFILING=false
            return 1
        fi

        log_success "Async Profiler检查通过: $PROFILER_SCRIPT"
        return 0
    fi
    return 0
}

# --- 1. 检查async profiler ---
check_async_profiler

# --- 2. 项目编译 ---
log_info "正在使用Maven编译项目..."
mvn clean package -DskipTests
log_success "项目编译完成!"

# --- 3. 实验配置 ---
# 定义JAR文件的路径
JAR_FILE="AFD-algorithms/experiment/target/experiment-1.0-SNAPSHOT-jar-with-dependencies.jar"

# 检查JAR文件是否存在
if [ ! -f "$JAR_FILE" ]; then
    log_warn "未找到JAR文件: $JAR_FILE"
    log_info "尝试使用优化启动脚本构建..."
    cd AFD-algorithms/experiment
    if [ -f "run-optimized-experiment.sh" ]; then
        chmod +x run-optimized-experiment.sh
        ./run-optimized-experiment.sh --dry-run
        cd ../..
    else
        echo "错误: 无法找到实验JAR文件"
        exit 1
    fi
fi

# 定义主类的完整名称
MAIN_CLASS="experiment.ExperimentRunner"

# --- 4. 128GB服务器内存配置 ---
log_info "配置128GB服务器专用JVM参数..."

# 检测系统内存
TOTAL_MEMORY_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
TOTAL_MEMORY_MB=$((TOTAL_MEMORY_KB / 1024))
log_info "检测到系统内存: ${TOTAL_MEMORY_MB}MB ($(echo "scale=1; ${TOTAL_MEMORY_MB}/1024" | bc)GB)"

# 检测大页面支持
HUGEPAGES_TOTAL=$(cat /proc/meminfo | grep HugePages_Total | awk '{print $2}')
HUGEPAGES_SIZE_KB=$(cat /proc/meminfo | grep Hugepagesize | awk '{print $2}')
if [ "$HUGEPAGES_TOTAL" -gt 0 ]; then
    HUGEPAGES_AVAILABLE_GB=$(echo "scale=1; $HUGEPAGES_TOTAL * $HUGEPAGES_SIZE_KB / 1024 / 1024" | bc)
    log_info "检测到大页面支持: ${HUGEPAGES_TOTAL}个 × ${HUGEPAGES_SIZE_KB}KB = ${HUGEPAGES_AVAILABLE_GB}GB"
    USE_LARGE_PAGES=true
else
    log_warn "未检测到大页面支持，建议配置大页面以提升性能"
    USE_LARGE_PAGES=false
fi

# 128GB服务器优化配置 - 解决Compressed OOPs警告
# 使用31GB堆内存以保持Compressed OOPs优势，配合更大的堆外缓存
HEAP_SIZE="31g"              # 31GB堆内存（保持Compressed OOPs）
NEW_SIZE="8g"                # 8GB新生代（约25%）
METASPACE_SIZE="2g"          # 2GB元空间
REGION_SIZE="32m"            # 32MB G1区域大小（适合31GB堆）
PAUSE_TARGET="100"           # 100ms GC暂停目标

# PLI缓存配置 - 超激进配置，利用剩余内存
PLI_CACHE_MB=49152           # 48GB PLI缓存（利用堆外内存）
PLI_CLEANUP_MB=40960         # 40GB 清理阈值

# 构建JVM参数
JVM_ARGS=(
    # 内存配置 - 优化的31GB堆内存配置
    "-Xms${HEAP_SIZE}"
    "-Xmx${HEAP_SIZE}"
    "-XX:NewSize=${NEW_SIZE}"
    "-XX:MaxNewSize=${NEW_SIZE}"
    "-XX:MetaspaceSize=${METASPACE_SIZE}"
    "-XX:MaxMetaspaceSize=${METASPACE_SIZE}"

    # 性能优化
    "-XX:+UnlockExperimentalVMOptions"
    "-XX:+UseStringDeduplication"
    "-XX:+OptimizeStringConcat"
    "-XX:+UseCompressedOops"
    "-XX:+UseCompressedClassPointers"

    # G1GC配置 - 针对31GB堆优化
    "-XX:+UseG1GC"
    "-XX:MaxGCPauseMillis=${PAUSE_TARGET}"
    "-XX:G1HeapRegionSize=${REGION_SIZE}"
    "-XX:G1NewSizePercent=25"
    "-XX:G1MaxNewSizePercent=35"
    "-XX:G1MixedGCCountTarget=8"
    "-XX:G1MixedGCLiveThresholdPercent=85"
    "-XX:G1OldCSetRegionThresholdPercent=10"
    "-XX:G1ReservePercent=15"
    
    # G1GC额外优化
    "-XX:+G1UseAdaptiveIHOP"
    "-XX:G1HeapWastePercent=5"
    "-XX:G1MixedGCLiveThresholdPercent=85"

    # 监控和调试
    "-Xlog:gc*=info:file=gc-128gb-$(date +%Y%m%d-%H%M%S).log:time,uptime,level,tags:filecount=10,filesize=100M"
    
    # PLI优化配置
    "-Dpli.cache.max.memory.mb=${PLI_CACHE_MB}"
    "-Dpli.cache.cleanup.threshold.mb=${PLI_CLEANUP_MB}"
    "-Dmemory.monitor.enabled=true"
    "-Dmemory.monitor.warning.threshold=0.8"
    "-Dmemory.monitor.critical.threshold=0.9"
    "-Dpli.performance.stats=true"
    "-Dpli.memory.stats=true"
    "-Dpli.enable.all.optimizations=true"
    "-Dstreaming.pli.chunk.size=300000"
    "-Dpli.cache.aggressive.mode=true"
    "-Dpli.cache.use.offheap=true"
)

# 根据大页面支持情况添加相应参数
if [ "$USE_LARGE_PAGES" = "true" ]; then
    JVM_ARGS+=(
        "-XX:+UseLargePages"
        "-XX:LargePageSizeInBytes=2m"
        "-XX:+AlwaysPreTouch"
    )
    log_info "启用大页面支持"
else
    # 不使用大页面时的优化
    JVM_ARGS+=(
        "-XX:+AlwaysPreTouch"
    )
    log_warn "未启用大页面，建议配置大页面以获得更好性能"
fi

# --- 5. 显示配置信息 ---
echo
echo "============================================================"
echo "                128GB服务器优化实验配置"
echo "============================================================"
echo "系统内存:     ${TOTAL_MEMORY_MB}MB ($(echo "scale=1; ${TOTAL_MEMORY_MB}/1024" | bc)GB)"
echo "堆内存大小:   ${HEAP_SIZE}"
echo "新生代大小:   ${NEW_SIZE}"
echo "元空间大小:   ${METASPACE_SIZE}"
echo "GC暂停目标:   ${PAUSE_TARGET}ms"
echo "PLI缓存限制:  ${PLI_CACHE_MB}MB ($(echo "scale=1; ${PLI_CACHE_MB}/1024" | bc)GB)"
echo "PLI清理阈值:  ${PLI_CLEANUP_MB}MB"
echo "PLI实现模式:  ${PLI_MODE}"
echo "数据集路径:   ${DATASET_PATH}"
echo "结果文件:     ${RESULTS_FILE}"
if [ "$ENABLE_PROFILING" = "true" ]; then
    echo "性能分析:     已启用 (async-profiler)"
else
    echo "性能分析:     已禁用"
fi
if [ "$USE_LARGE_PAGES" = "true" ]; then
    echo "大页面支持:   已启用 (${HUGEPAGES_AVAILABLE_GB}GB可用)"
else
    echo "大页面支持:   未启用 (建议配置以提升性能)"
fi
echo "============================================================"

# --- 6. 准备性能分析 ---
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
FLAME_GRAPH_FILE="flamegraph_${PLI_MODE}_${TIMESTAMP}.html"
JFR_FILE="profiling_${PLI_MODE}_${TIMESTAMP}.jfr"

if [ "$ENABLE_PROFILING" = "true" ]; then
    log_info "准备性能分析文件:"
    log_info "- 火焰图文件: $FLAME_GRAPH_FILE"
    log_info "- JFR文件: $JFR_FILE"
fi

# --- 7. 执行实验 ---
echo
log_info "开始执行128GB服务器优化实验..."
echo ">>  数据集路径: ${DATASET_PATH}"
echo ">>  PLI模式: ${PLI_MODE}"
echo ">>  采样模式: ALL"
echo ">>  运行TANE: true"
echo ">>  结果文件: ${RESULTS_FILE} (追加模式)"
echo ">>  超时时间: 120分钟"
echo ">>  内存配置: 31GB堆内存 + 48GB PLI缓存 (优化Compressed OOPs)"
if [ "$ENABLE_PROFILING" = "true" ]; then
    echo ">>  性能分析: 已启用"
fi
echo "-------------------------------------------------"

# 构建完整的Java命令
JAVA_CMD=(
    "java"
    "${JVM_ARGS[@]}"
    "-cp" "$JAR_FILE"
    "$MAIN_CLASS"
    "--dataset" "${DATASET_PATH}"
    "--results-file" "${RESULTS_FILE}"
    "--pli-mode" "${PLI_MODE}"
    "--sampling-mode" "ALL"
    "--run-mode" "APPEND"
    "--run-tane" "true"
    "--timeout" "120"
    "--max-error" "0.05"
    "--sample-param" "200"
    "--seed" "114514"
)

# 显示完整命令（可选）
if [ "${DEBUG:-false}" = "true" ]; then
    log_info "执行命令:"
    printf '%s ' "${JAVA_CMD[@]}"
    echo
    echo
fi

# 启动性能分析（如果启用）
if [ "$ENABLE_PROFILING" = "true" ]; then
    log_info "启动async profiler..."

    # 启动Java程序并获取PID
    log_info "启动实验程序..."
    "${JAVA_CMD[@]}" &
    JAVA_PID=$!

    # 等待Java程序启动
    sleep 5

    # 检查Java程序是否还在运行
    if ! kill -0 $JAVA_PID 2>/dev/null; then
        log_error "Java程序启动失败"
        exit 1
    fi

    log_info "Java程序PID: $JAVA_PID"

    # 启动profiling
    log_info "开始性能分析..."
    $PROFILER_SCRIPT -e cpu,alloc -d $PROFILING_DURATION -i $PROFILING_INTERVAL -f $FLAME_GRAPH_FILE $JAVA_PID &
    PROFILER_PID=$!

    # 等待Java程序完成
    log_info "等待实验完成..."
    wait $JAVA_PID
    JAVA_EXIT_CODE=$?

    # 停止profiler
    if kill -0 $PROFILER_PID 2>/dev/null; then
        log_info "停止性能分析..."
        kill $PROFILER_PID 2>/dev/null || true
        wait $PROFILER_PID 2>/dev/null || true
    fi

else
    # 不使用性能分析，直接执行
    log_info "启动实验..."
    "${JAVA_CMD[@]}"
    JAVA_EXIT_CODE=$?
fi

# 检查执行结果
if [ $JAVA_EXIT_CODE -eq 0 ]; then
    echo
    log_success "所有实验已成功完成!"
    log_success "你可以在 ${RESULTS_FILE} 文件中查看结果。"

    # 显示GC日志文件位置
    GC_LOG=$(ls -t gc-128gb-*.log 2>/dev/null | head -n1)
    if [ -n "$GC_LOG" ]; then
        log_info "GC日志文件: $GC_LOG"
    fi

    # 显示性能分析结果
    if [ "$ENABLE_PROFILING" = "true" ]; then
        echo
        log_success "性能分析完成!"
        if [ -f "$FLAME_GRAPH_FILE" ]; then
            log_info "火焰图文件: $FLAME_GRAPH_FILE"
            log_info "查看火焰图的方法:"
            log_info "1. 将文件下载到本地计算机"
            log_info "2. 使用浏览器打开HTML文件"
            log_info "3. 或者在服务器上使用: python3 -m http.server 8000"
            log_info "   然后在浏览器访问: http://服务器IP:8000/$FLAME_GRAPH_FILE"
        else
            log_warn "火焰图文件未生成: $FLAME_GRAPH_FILE"
        fi

        if [ -f "$JFR_FILE" ]; then
            log_info "JFR文件: $JFR_FILE"
            log_info "可以使用JProfiler或VisualVM分析JFR文件"
        fi
    fi

else
    echo
    log_error "实验执行失败，退出码: $JAVA_EXIT_CODE"
    log_warn "请检查日志以获取更多信息。"
    exit $JAVA_EXIT_CODE
fi

# --- 使用说明和安装指导 ---
cat << 'EOF'

============================================================
                    使用说明和配置指导
============================================================

## PLI模式说明
- original: 仅使用原始PLI实现，适合性能对比测试
- dynamic:  动态切换PLI实现，根据数据集大小和内存情况自动优化

## Async Profiler安装指导（首次使用）

### 1. 下载安装
```bash
# 创建安装目录
sudo mkdir -p /opt/async-profiler
cd /tmp

# 下载最新版本（请访问GitHub获取最新版本号）
wget https://github.com/jvm-profiling-tools/async-profiler/releases/download/v2.9/async-profiler-2.9-linux-x64.tar.gz

# 解压到安装目录
sudo tar -xzf async-profiler-2.9-linux-x64.tar.gz -C /opt/async-profiler --strip-components=1

# 设置权限
sudo chmod +x /opt/async-profiler/profiler.sh
```

### 2. SSH服务器环境配置
```bash
# 如果遇到权限问题，可能需要调整内核参数
echo 'kernel.perf_event_paranoid=1' | sudo tee -a /etc/sysctl.conf
echo 'kernel.kptr_restrict=0' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# 或者临时设置
sudo sysctl kernel.perf_event_paranoid=1
sudo sysctl kernel.kptr_restrict=0
```

### 3. 验证安装
```bash
/opt/async-profiler/profiler.sh --help
```

### 4. 自定义配置
如果async-profiler安装在其他位置，请修改脚本中的ASYNC_PROFILER_PATH变量。

## 火焰图查看方法

### 方法1: 下载到本地
```bash
# 使用scp下载火焰图文件
scp user@server:/path/to/flamegraph_*.html ./
# 然后用浏览器打开HTML文件
```

### 方法2: 服务器HTTP服务
```bash
# 在实验目录启动HTTP服务器
python3 -m http.server 8000
# 浏览器访问: http://服务器IP:8000/flamegraph_*.html
```

### 方法3: 使用VS Code Remote
如果使用VS Code Remote SSH，可以直接在VS Code中打开HTML文件。

============================================================
EOF

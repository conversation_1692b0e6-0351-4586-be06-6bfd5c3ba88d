# PLI算法性能分析指南

本指南介绍如何使用修改后的实验框架进行PLI算法实现切换和性能分析。

## 功能概述

### 1. PLI算法实现模式
- **original**: 仅使用原始PLI实现
- **dynamic**: 动态切换PLI实现（原始实现、优化缓存、流式处理）

### 2. 性能分析功能
- 集成async-profiler进行CPU和内存分析
- 自动生成火焰图HTML文件
- 支持JFR文件生成
- 时间戳命名避免文件覆盖

## 快速开始

### 1. 基本使用

```bash
# 使用原始PLI实现运行实验
./run_experiments.sh

# 或者显式指定PLI模式
PLI_MODE=original ./run_experiments.sh
PLI_MODE=dynamic ./run_experiments.sh
```

### 2. 自定义配置

编辑 `run_experiments.sh` 中的配置变量：

```bash
# 数据集配置
DATASET_PATH='data/int/EQ-500K-12.csv'  # 数据集路径
RESULTS_FILE='result/result0807_test.csv'  # 结果文件

# PLI模式配置
PLI_MODE='original'  # 或 'dynamic'

# 性能分析配置
ENABLE_PROFILING=true  # 启用/禁用性能分析
ASYNC_PROFILER_PATH="/opt/async-profiler"  # async-profiler安装路径
```

### 3. 命令行参数

ExperimentRunner现在支持以下新参数：

```bash
java -jar experiment.jar \
  --dataset data/int/EQ-500K-12.csv \
  --results-file result/result0807_test.csv \
  --pli-mode original \  # 或 dynamic
  --sampling-mode ALL \
  --run-tane true \
  --timeout 120
```

## Async Profiler安装

### 首次安装步骤

1. **下载async-profiler v4.1**
```bash
cd /tmp
wget https://github.com/jvm-profiling-tools/async-profiler/releases/download/v4.1/async-profiler-4.1-linux-x64.tar.gz
```

2. **安装到系统目录**
```bash
sudo mkdir -p /opt/async-profiler
sudo tar -xzf async-profiler-4.1-linux-x64.tar.gz -C /opt/async-profiler --strip-components=1
sudo chmod +x /opt/async-profiler/bin/asprof
```

3. **配置内核参数（SSH服务器环境）**
```bash
# 临时设置
sudo sysctl kernel.perf_event_paranoid=1
sudo sysctl kernel.kptr_restrict=0

# 永久设置
echo 'kernel.perf_event_paranoid=1' | sudo tee -a /etc/sysctl.conf
echo 'kernel.kptr_restrict=0' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

4. **验证安装**
```bash
/opt/async-profiler/bin/asprof --help
```

5. **版本差异说明**
async-profiler v4.1的主要变化：
- 可执行文件从`profiler.sh`改为`bin/asprof`
- 命令语法更简洁：`asprof -d 30 -f flamegraph.html <PID>`
- 支持更多的事件类型和输出格式
- 改进的性能和稳定性

### 常见问题解决

1. **权限问题**
```bash
# 如果遇到"No access to perf events"错误
sudo sysctl kernel.perf_event_paranoid=1
```

2. **路径问题**
```bash
# 如果async-profiler安装在其他位置，修改脚本中的路径
ASYNC_PROFILER_PATH="/your/custom/path/async-profiler"
```

3. **可执行文件问题**
```bash
# 确认asprof可执行文件存在且有权限
ls -la /opt/async-profiler/bin/asprof
# 如果权限不足，设置执行权限
sudo chmod +x /opt/async-profiler/bin/asprof
```

## 火焰图分析

### 查看火焰图的方法

1. **下载到本地查看**
```bash
scp user@server:/path/to/flamegraph_*.html ./
# 用浏览器打开HTML文件
```

2. **服务器HTTP服务**
```bash
cd /path/to/experiment/directory
python3 -m http.server 8000
# 浏览器访问: http://服务器IP:8000/flamegraph_*.html
```

3. **VS Code Remote**
如果使用VS Code Remote SSH，可以直接在编辑器中打开HTML文件。

### 火焰图解读

- **宽度**: 表示函数调用的时间占比
- **高度**: 表示调用栈的深度
- **颜色**: 不同的函数或包
- **可交互**: 点击可以放大查看特定区域

## 性能对比分析

### 1. 运行对比实验

```bash
# 运行原始实现
PLI_MODE=original ./run_experiments.sh

# 运行动态切换实现
PLI_MODE=dynamic ./run_experiments.sh
```

### 2. 分析结果

1. **查看实验结果CSV文件**
   - 比较执行时间
   - 比较内存使用
   - 比较PLI统计信息

2. **分析火焰图**
   - 对比CPU热点
   - 分析内存分配模式
   - 识别性能瓶颈

3. **查看GC日志**
   - 分析垃圾回收行为
   - 比较GC暂停时间

## 故障排除

### 1. 实验失败
- 检查数据集路径是否正确
- 确认有足够的内存和磁盘空间
- 查看控制台输出和错误信息

### 2. 性能分析失败
- 确认async-profiler正确安装
- 检查内核参数设置
- 验证权限配置

### 3. 火焰图无法生成
- 检查async-profiler路径配置
- 确认Java程序正常运行
- 查看profiler错误输出

## 最佳实践

1. **实验前准备**
   - 确保系统内存充足
   - 关闭不必要的后台程序
   - 备份重要数据

2. **性能分析**
   - 先运行小数据集验证配置
   - 对比不同PLI模式的性能
   - 记录实验环境和配置

3. **结果分析**
   - 结合多个指标综合分析
   - 关注内存使用模式
   - 识别算法瓶颈

## 技术支持

如果遇到问题，请检查：
1. 系统环境配置
2. 软件版本兼容性
3. 权限和路径设置
4. 日志文件中的错误信息

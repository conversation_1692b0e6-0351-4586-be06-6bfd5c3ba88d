#!/bin/bash

# 调试async-profiler Agent模式和PLI模式问题的专用脚本
# 用于诊断火焰图生成失败和PLI模式控制无效的问题

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "============================================================"
echo "        Async Profiler Agent模式和PLI模式调试"
echo "============================================================"

# 配置变量
ASYNC_PROFILER_PATH="/opt/async-profiler"
ASYNC_PROFILER_LIB="${ASYNC_PROFILER_PATH}/lib/libasyncProfiler.so"
TEST_OUTPUT_DIR="$(pwd)/debug_output"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 创建测试输出目录
mkdir -p "$TEST_OUTPUT_DIR"

# 1. 测试Agent库基本功能
log_info "测试1: Agent库基本功能"

if [ ! -f "$ASYNC_PROFILER_LIB" ]; then
    log_error "Agent库文件不存在: $ASYNC_PROFILER_LIB"
    exit 1
fi

log_success "Agent库文件存在: $ASYNC_PROFILER_LIB"

# 测试Agent参数格式
TEST_FLAME_FILE="${TEST_OUTPUT_DIR}/test_agent_${TIMESTAMP}.html"
TEST_AGENT_PARAMS="start,event=cpu,interval=10ms,file=${TEST_FLAME_FILE}"

log_info "测试Agent参数格式: $TEST_AGENT_PARAMS"

# 创建简单的Java测试程序
cat > "${TEST_OUTPUT_DIR}/TestApp.java" << 'EOF'
public class TestApp {
    public static void main(String[] args) {
        System.out.println("开始测试程序...");
        
        // 模拟一些CPU活动
        long start = System.currentTimeMillis();
        while (System.currentTimeMillis() - start < 10000) { // 运行10秒
            for (int i = 0; i < 1000000; i++) {
                Math.sqrt(i);
            }
            
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                break;
            }
        }
        
        System.out.println("测试程序完成");
    }
}
EOF

# 编译测试程序
log_info "编译测试程序..."
if ! javac "${TEST_OUTPUT_DIR}/TestApp.java"; then
    log_error "测试程序编译失败"
    exit 1
fi

# 2. 测试Agent模式
log_info "测试2: Agent模式火焰图生成"

log_info "启动带Agent的Java程序..."
cd "$TEST_OUTPUT_DIR"

# 构建测试命令
AGENT_PATH_PARAM="-agentpath:${ASYNC_PROFILER_LIB}=${TEST_AGENT_PARAMS}"
TEST_CMD=(
    "java"
    "$AGENT_PATH_PARAM"
    "TestApp"
)

log_info "测试命令: ${TEST_CMD[*]}"

# 执行测试
if "${TEST_CMD[@]}"; then
    log_success "Java程序执行完成"
else
    log_error "Java程序执行失败"
    exit 1
fi

# 检查火焰图是否生成
if [ -f "$TEST_FLAME_FILE" ]; then
    log_success "火焰图生成成功: $TEST_FLAME_FILE"
    log_info "文件大小: $(du -h "$TEST_FLAME_FILE" | cut -f1)"
    
    # 检查文件内容
    if grep -q "flamegraph" "$TEST_FLAME_FILE" 2>/dev/null; then
        log_success "火焰图文件格式正确"
    else
        log_warn "火焰图文件可能格式异常"
    fi
else
    log_error "火焰图文件未生成: $TEST_FLAME_FILE"
    
    # 检查是否有其他输出文件
    log_info "检查输出目录中的所有文件:"
    ls -la "$TEST_OUTPUT_DIR"
    
    # 检查是否有错误日志
    if [ -f "hs_err_pid*.log" ]; then
        log_warn "发现JVM错误日志:"
        ls -la hs_err_pid*.log
    fi
fi

cd - >/dev/null

# 3. 测试PLI模式配置
log_info "测试3: PLI模式配置"

# 检查run_experiments.sh中的PLI配置
if [ ! -f "run_experiments.sh" ]; then
    log_error "run_experiments.sh文件不存在"
    exit 1
fi

# 检查PLI模式相关的JVM参数
log_info "检查PLI模式JVM参数配置..."

if grep -q "pli.force.original.only" run_experiments.sh; then
    log_success "发现PLI原始模式配置"
else
    log_error "缺少PLI原始模式配置"
fi

if grep -q "pli.disable.optimization" run_experiments.sh; then
    log_success "发现PLI优化禁用配置"
else
    log_error "缺少PLI优化禁用配置"
fi

# 4. 测试完整的实验配置
log_info "测试4: 完整实验配置验证"

# 模拟run_experiments.sh的配置
PLI_MODE="original"
ENABLE_PROFILING=true
ENABLE_CPU_PROFILING=true
CPU_PROFILING_EVENT="cpu"
PROFILING_INTERVAL="10ms"

# 构建Agent参数
FULL_TEST_FLAME_FILE="${TEST_OUTPUT_DIR}/full_test_${TIMESTAMP}.html"
FULL_AGENT_PARAMS="start,event=${CPU_PROFILING_EVENT},interval=${PROFILING_INTERVAL},file=${FULL_TEST_FLAME_FILE}"

log_info "完整Agent参数: $FULL_AGENT_PARAMS"

# 构建JVM参数数组
JVM_ARGS=(
    "-agentpath:${ASYNC_PROFILER_LIB}=${FULL_AGENT_PARAMS}"
)

# 根据PLI模式添加系统属性
if [ "$PLI_MODE" = "original" ]; then
    JVM_ARGS+=(
        "-Dpli.force.original.only=true"
        "-Dpli.disable.optimization=true"
        "-Dpli.disable.dynamic.switching=true"
        "-Dpli.force.streaming=false"
        "-Dpli.enable.optimization.integrator=false"
    )
    log_info "添加原始PLI模式的JVM参数"
fi

log_info "完整JVM参数:"
for arg in "${JVM_ARGS[@]}"; do
    log_info "  $arg"
done

# 5. 测试ExperimentRunner的PLI模式处理
log_info "测试5: ExperimentRunner PLI模式处理"

# 检查JAR文件
JAR_FILE="AFD-algorithms/experiment/target/experiment-1.0-SNAPSHOT-jar-with-dependencies.jar"
if [ ! -f "$JAR_FILE" ]; then
    log_warn "实验JAR文件不存在: $JAR_FILE"
    log_info "尝试编译..."
    if command -v mvn >/dev/null 2>&1; then
        mvn clean package -DskipTests -q
        if [ -f "$JAR_FILE" ]; then
            log_success "JAR文件编译成功"
        else
            log_error "JAR文件编译失败"
        fi
    else
        log_error "Maven不可用，无法编译JAR文件"
    fi
fi

if [ -f "$JAR_FILE" ]; then
    # 测试PLI模式参数传递
    log_info "测试PLI模式参数传递..."
    
    # 创建测试数据集
    if [ ! -f "data/airport.csv" ]; then
        log_warn "测试数据集不存在，跳过ExperimentRunner测试"
    else
        # 构建测试命令
        EXPERIMENT_TEST_CMD=(
            "java"
            "${JVM_ARGS[@]}"
            "-cp" "$JAR_FILE"
            "experiment.ExperimentRunner"
            "--dataset" "data/airport.csv"
            "--pli-mode" "original"
            "--results-file" "${TEST_OUTPUT_DIR}/test_results.csv"
            "--timeout" "1"
            "--sampling-mode" "NO_SAMPLING"
            "--run-tane" "false"
        )
        
        log_info "测试ExperimentRunner命令:"
        printf '%s ' "${EXPERIMENT_TEST_CMD[@]}"
        echo
        
        # 执行测试（限制时间）
        timeout 60s "${EXPERIMENT_TEST_CMD[@]}" || {
            log_warn "ExperimentRunner测试超时或失败，但这可能是正常的"
        }
        
        # 检查火焰图是否生成
        if [ -f "$FULL_TEST_FLAME_FILE" ]; then
            log_success "ExperimentRunner火焰图生成成功: $FULL_TEST_FLAME_FILE"
        else
            log_warn "ExperimentRunner火焰图未生成"
        fi
    fi
fi

# 6. 生成诊断报告
log_info "生成诊断报告..."

REPORT_FILE="${TEST_OUTPUT_DIR}/diagnostic_report_${TIMESTAMP}.txt"

cat > "$REPORT_FILE" << EOF
Async Profiler Agent模式和PLI模式诊断报告
生成时间: $(date)

=== 系统环境 ===
操作系统: $(uname -a)
Java版本: $(java -version 2>&1 | head -n1)
可用内存: $(free -h | grep Mem | awk '{print $7}')

=== Async Profiler配置 ===
安装路径: $ASYNC_PROFILER_PATH
Agent库: $ASYNC_PROFILER_LIB
库文件存在: $([ -f "$ASYNC_PROFILER_LIB" ] && echo "是" || echo "否")
库文件权限: $(ls -la "$ASYNC_PROFILER_LIB" 2>/dev/null || echo "无法访问")

=== 测试结果 ===
基本Agent测试: $([ -f "$TEST_FLAME_FILE" ] && echo "通过" || echo "失败")
完整配置测试: $([ -f "$FULL_TEST_FLAME_FILE" ] && echo "通过" || echo "失败")

=== 生成的文件 ===
EOF

# 列出生成的文件
ls -la "$TEST_OUTPUT_DIR" >> "$REPORT_FILE"

log_success "诊断报告生成: $REPORT_FILE"

# 7. 总结和建议
echo
echo "============================================================"
echo "                诊断总结和建议"
echo "============================================================"

if [ -f "$TEST_FLAME_FILE" ]; then
    log_success "Agent模式基本功能正常"
    log_info "建议:"
    log_info "1. 检查run_experiments.sh中的输出路径配置"
    log_info "2. 确保输出目录有写入权限"
    log_info "3. 启用DEBUG_MODE=true获取更多调试信息"
else
    log_error "Agent模式存在问题"
    log_info "可能的解决方案:"
    log_info "1. 检查async-profiler版本兼容性"
    log_info "2. 验证Agent参数格式"
    log_info "3. 检查系统权限和SELinux设置"
    log_info "4. 尝试使用绝对路径"
fi

echo
log_info "PLI模式配置建议:"
log_info "1. 确保JVM系统属性在configureMemoryOptimization()之前设置"
log_info "2. 使用-Dpli.force.original.only=true强制原始模式"
log_info "3. 检查ExperimentRunner的日志输出确认PLI模式"

echo
log_info "调试文件位置: $TEST_OUTPUT_DIR"
log_info "详细报告: $REPORT_FILE"

echo "============================================================"

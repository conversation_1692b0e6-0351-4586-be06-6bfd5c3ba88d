[2025-07-28T21:18:21.530+0800][0.007s][info][gc] Using G1
[2025-07-28T21:18:21.537+0800][0.014s][info][gc,init] Version: 17.0.15+6-Ubuntu-0ubuntu120.04 (release)
[2025-07-28T21:18:21.537+0800][0.015s][info][gc,init] CPUs: 28 total, 28 available
[2025-07-28T21:18:21.538+0800][0.015s][info][gc,init] Memory: 15833M
[2025-07-28T21:18:21.538+0800][0.015s][info][gc,init] Large Page Support: Disabled
[2025-07-28T21:18:21.539+0800][0.016s][info][gc,init] NUMA Support: Disabled
[2025-07-28T21:18:21.539+0800][0.016s][info][gc,init] Compressed Oops: Enabled (Zero based)
[2025-07-28T21:18:21.539+0800][0.016s][info][gc,init] Heap Region Size: 16M
[2025-07-28T21:18:21.540+0800][0.017s][info][gc,init] Heap Min Capacity: 6G
[2025-07-28T21:18:21.540+0800][0.017s][info][gc,init] Heap Initial Capacity: 6G
[2025-07-28T21:18:21.540+0800][0.017s][info][gc,init] Heap Max Capacity: 6G
[2025-07-28T21:18:21.540+0800][0.018s][info][gc,init] Pre-touch: Disabled
[2025-07-28T21:18:21.541+0800][0.018s][info][gc,init] Parallel Workers: 20
[2025-07-28T21:18:21.541+0800][0.018s][info][gc,init] Concurrent Workers: 5
[2025-07-28T21:18:21.541+0800][0.018s][info][gc,init] Concurrent Refinement Workers: 20
[2025-07-28T21:18:21.541+0800][0.019s][info][gc,init] Periodic GC: Disabled
[2025-07-28T21:18:21.548+0800][0.025s][info][gc,metaspace] CDS archive(s) mapped at: [0x000078b91b000000-0x000078b91bbc7000-0x000078b91bbc7000), size 12349440, SharedBaseAddress: 0x000078b91b000000, ArchiveRelocationMode: 1.
[2025-07-28T21:18:21.548+0800][0.025s][info][gc,metaspace] Compressed class space mapped at: 0x000078b91c000000-0x000078b95c000000, reserved size: 1073741824
[2025-07-28T21:18:21.548+0800][0.025s][info][gc,metaspace] Narrow klass base: 0x000078b91b000000, Narrow klass shift: 0, Narrow klass range: 0x100000000
[2025-07-28T21:19:12.944+0800][51.426s][info][gc,start    ] GC(0) Pause Young (Normal) (G1 Evacuation Pause)
[2025-07-28T21:19:12.945+0800][51.428s][info][gc,task     ] GC(0) Using 20 workers of 20 for evacuation
[2025-07-28T21:19:12.948+0800][51.431s][info][gc,phases   ] GC(0)   Pre Evacuate Collection Set: 0.2ms
[2025-07-28T21:19:12.949+0800][51.431s][info][gc,phases   ] GC(0)   Merge Heap Roots: 0.3ms
[2025-07-28T21:19:12.949+0800][51.431s][info][gc,phases   ] GC(0)   Evacuate Collection Set: 1.3ms
[2025-07-28T21:19:12.949+0800][51.432s][info][gc,phases   ] GC(0)   Post Evacuate Collection Set: 0.7ms
[2025-07-28T21:19:12.949+0800][51.432s][info][gc,phases   ] GC(0)   Other: 2.2ms
[2025-07-28T21:19:12.950+0800][51.432s][info][gc,heap     ] GC(0) Eden regions: 96->0(95)
[2025-07-28T21:19:12.950+0800][51.432s][info][gc,heap     ] GC(0) Survivor regions: 0->1(12)
[2025-07-28T21:19:12.950+0800][51.432s][info][gc,heap     ] GC(0) Old regions: 0->0
[2025-07-28T21:19:12.950+0800][51.433s][info][gc,heap     ] GC(0) Archive regions: 2->2
[2025-07-28T21:19:12.951+0800][51.433s][info][gc,heap     ] GC(0) Humongous regions: 0->0
[2025-07-28T21:19:12.951+0800][51.434s][info][gc,metaspace] GC(0) Metaspace: 5036K(5184K)->5036K(5184K) NonClass: 4359K(4416K)->4359K(4416K) Class: 676K(768K)->676K(768K)
[2025-07-28T21:19:12.951+0800][51.434s][info][gc          ] GC(0) Pause Young (Normal) (G1 Evacuation Pause) 1551M->17M(6144M) 7.802ms
[2025-07-28T21:19:12.952+0800][51.434s][info][gc,cpu      ] GC(0) User=0.01s Sys=0.01s Real=0.01s
[2025-07-28T21:19:43.076+0800][81.560s][info][gc,heap,exit] Heap
[2025-07-28T21:19:43.076+0800][81.560s][info][gc,heap,exit]  garbage-first heap   total 6291456K, used 788361K [0x0000000680000000, 0x0000000800000000)
[2025-07-28T21:19:43.076+0800][81.560s][info][gc,heap,exit]   region size 16384K, 48 young (786432K), 1 survivors (16384K)
[2025-07-28T21:19:43.076+0800][81.560s][info][gc,heap,exit]  Metaspace       used 5090K, committed 5312K, reserved 1114112K
[2025-07-28T21:19:43.077+0800][81.560s][info][gc,heap,exit]   class space    used 683K, committed 768K, reserved 1048576K

#!/bin/bash

# 内存配置验证脚本
# 验证新的保守内存配置是否能够成功启动

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "============================================================"
echo "        内存配置验证脚本"
echo "============================================================"

# 1. 检查系统内存状态
log_info "检查系统内存状态..."

TOTAL_MEMORY_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
AVAILABLE_MEMORY_KB=$(grep MemAvailable /proc/meminfo | awk '{print $2}' 2>/dev/null || grep MemFree /proc/meminfo | awk '{print $2}')
FREE_MEMORY_KB=$(grep MemFree /proc/meminfo | awk '{print $2}')

TOTAL_MEMORY_GB=$((TOTAL_MEMORY_KB / 1024 / 1024))
AVAILABLE_MEMORY_GB=$((AVAILABLE_MEMORY_KB / 1024 / 1024))
FREE_MEMORY_GB=$((FREE_MEMORY_KB / 1024 / 1024))

echo "系统内存状态:"
echo "  总内存: ${TOTAL_MEMORY_GB}GB"
echo "  可用内存: ${AVAILABLE_MEMORY_GB}GB"
echo "  空闲内存: ${FREE_MEMORY_GB}GB"

# 2. 测试不同的内存配置
log_info "测试不同的内存配置..."

# 测试配置数组
declare -a TEST_CONFIGS=(
    "32g:8192:保守配置"
    "48g:16384:中等配置"
    "64g:24576:推荐配置"
    "80g:32768:激进配置"
)

for config in "${TEST_CONFIGS[@]}"; do
    IFS=':' read -r heap_size pli_cache_mb description <<< "$config"
    
    echo
    log_info "测试 $description (堆:${heap_size}, PLI:$((pli_cache_mb/1024))GB)..."
    
    # 计算总内存需求
    heap_gb=${heap_size%g}
    pli_gb=$((pli_cache_mb / 1024))
    safety_margin=16
    total_required=$((heap_gb + pli_gb + safety_margin))
    
    echo "  内存需求分析:"
    echo "    堆内存: ${heap_gb}GB"
    echo "    PLI缓存: ${pli_gb}GB"
    echo "    系统预留: ${safety_margin}GB"
    echo "    总需求: ${total_required}GB"
    echo "    可用内存: ${AVAILABLE_MEMORY_GB}GB"
    
    if [ $total_required -le $AVAILABLE_MEMORY_GB ]; then
        log_success "内存需求检查通过"
        
        # 测试Java内存分配
        log_info "测试Java内存分配..."
        if java -Xms${heap_size} -Xmx${heap_size} -version >/dev/null 2>&1; then
            log_success "Java内存分配测试通过"
        else
            log_error "Java内存分配测试失败"
        fi
    else
        log_warn "内存需求超出可用内存 (需求:${total_required}GB > 可用:${AVAILABLE_MEMORY_GB}GB)"
    fi
done

# 3. 检查大页配置
echo
log_info "检查大页配置..."

HUGEPAGES_TOTAL=$(cat /proc/meminfo | grep HugePages_Total | awk '{print $2}' 2>/dev/null || echo "0")
HUGEPAGES_SIZE_KB=$(cat /proc/meminfo | grep Hugepagesize | awk '{print $2}' 2>/dev/null || echo "2048")
HUGEPAGES_FREE=$(cat /proc/meminfo | grep HugePages_Free | awk '{print $2}' 2>/dev/null || echo "0")

if [ "$HUGEPAGES_TOTAL" -gt 0 ]; then
    HUGEPAGES_TOTAL_GB=$(echo "scale=1; $HUGEPAGES_TOTAL * $HUGEPAGES_SIZE_KB / 1024 / 1024" | bc 2>/dev/null || echo "0")
    HUGEPAGES_FREE_GB=$(echo "scale=1; $HUGEPAGES_FREE * $HUGEPAGES_SIZE_KB / 1024 / 1024" | bc 2>/dev/null || echo "0")
    
    echo "大页配置:"
    echo "  总大页数: $HUGEPAGES_TOTAL"
    echo "  空闲大页数: $HUGEPAGES_FREE"
    echo "  大页大小: ${HUGEPAGES_SIZE_KB}KB"
    echo "  总大页容量: ${HUGEPAGES_TOTAL_GB}GB"
    echo "  空闲大页容量: ${HUGEPAGES_FREE_GB}GB"
    
    if [ "$HUGEPAGES_FREE" -gt 0 ]; then
        log_success "大页配置可用"
    else
        log_warn "大页已配置但无空闲大页"
    fi
else
    log_warn "未配置大页，建议配置以提升性能"
    echo "配置大页的命令:"
    echo "  sudo echo 8192 > /proc/sys/vm/nr_hugepages  # 配置16GB大页"
fi

# 4. 检查系统限制
echo
log_info "检查系统限制..."

# 检查虚拟内存限制
VMEM_LIMIT=$(ulimit -v 2>/dev/null || echo "unlimited")
echo "虚拟内存限制: $VMEM_LIMIT"

# 检查进程内存限制
MEM_LIMIT=$(ulimit -m 2>/dev/null || echo "unlimited")
echo "进程内存限制: $MEM_LIMIT"

# 检查文件描述符限制
FD_LIMIT=$(ulimit -n 2>/dev/null || echo "unknown")
echo "文件描述符限制: $FD_LIMIT"

# 5. 检查交换空间
echo
log_info "检查交换空间..."

SWAP_TOTAL_KB=$(grep SwapTotal /proc/meminfo | awk '{print $2}')
SWAP_FREE_KB=$(grep SwapFree /proc/meminfo | awk '{print $2}')
SWAP_TOTAL_GB=$((SWAP_TOTAL_KB / 1024 / 1024))
SWAP_FREE_GB=$((SWAP_FREE_KB / 1024 / 1024))

echo "交换空间:"
echo "  总交换空间: ${SWAP_TOTAL_GB}GB"
echo "  空闲交换空间: ${SWAP_FREE_GB}GB"

if [ "$SWAP_TOTAL_GB" -lt 16 ]; then
    log_warn "交换空间较小，建议增加到至少16GB"
    echo "增加交换空间的命令:"
    echo "  sudo fallocate -l 16G /swapfile"
    echo "  sudo chmod 600 /swapfile"
    echo "  sudo mkswap /swapfile"
    echo "  sudo swapon /swapfile"
fi

# 6. 测试run_experiments.sh的内存验证功能
echo
log_info "测试run_experiments.sh的内存验证功能..."

if [ -f "run_experiments.sh" ]; then
    # 提取内存验证函数并测试
    if grep -q "validate_and_adjust_memory_config" run_experiments.sh; then
        log_success "内存验证函数存在于run_experiments.sh中"
    else
        log_error "内存验证函数不存在于run_experiments.sh中"
    fi
    
    # 检查配置变量
    if grep -q "ENABLE_MEMORY_VALIDATION=true" run_experiments.sh; then
        log_success "内存验证已启用"
    else
        log_warn "内存验证未启用"
    fi
    
    # 检查备用配置
    if grep -q "FALLBACK_HEAP_SIZE" run_experiments.sh; then
        log_success "备用配置已设置"
    else
        log_warn "备用配置未设置"
    fi
else
    log_error "run_experiments.sh文件不存在"
fi

# 7. 推荐配置
echo
echo "============================================================"
echo "                推荐配置"
echo "============================================================"

if [ $AVAILABLE_MEMORY_GB -ge 100 ]; then
    echo "推荐配置 (可用内存充足):"
    echo "  HEAP_SIZE=\"64g\""
    echo "  PLI_CACHE_MB=24576"
    echo "  预期成功率: 高"
elif [ $AVAILABLE_MEMORY_GB -ge 80 ]; then
    echo "推荐配置 (可用内存中等):"
    echo "  HEAP_SIZE=\"48g\""
    echo "  PLI_CACHE_MB=16384"
    echo "  预期成功率: 高"
else
    echo "推荐配置 (可用内存较少):"
    echo "  HEAP_SIZE=\"32g\""
    echo "  PLI_CACHE_MB=8192"
    echo "  预期成功率: 很高"
fi

# 8. 总结
echo
echo "============================================================"
echo "                验证总结"
echo "============================================================"

log_info "验证完成！"
echo
echo "关键发现:"
echo "- 系统总内存: ${TOTAL_MEMORY_GB}GB"
echo "- 可用内存: ${AVAILABLE_MEMORY_GB}GB"
echo "- 大页状态: $([ "$HUGEPAGES_TOTAL" -gt 0 ] && echo "已配置" || echo "未配置")"
echo "- 交换空间: ${SWAP_TOTAL_GB}GB"

echo
echo "建议操作:"
if [ $AVAILABLE_MEMORY_GB -ge 90 ]; then
    echo "1. 可以使用64GB堆内存配置"
    echo "2. 启用内存验证功能"
    echo "3. 配置大页以提升性能"
else
    echo "1. 使用保守的48GB堆内存配置"
    echo "2. 释放系统内存"
    echo "3. 增加交换空间"
fi

echo
echo "下一步:"
echo "1. 根据推荐配置修改run_experiments.sh"
echo "2. 运行 ./run_experiments.sh 进行实际测试"
echo "3. 监控内存使用情况"

echo "============================================================"

# Async Profiler Agent模式迁移指南

本文档详细说明了从attach模式切换到agent模式的修改内容和使用方法。

## 🎯 迁移概述

### 问题背景
- **Attach模式限制**: 不指定`-d`参数时，默认运行10秒后自动停止
- **需求**: profiler需要跟随Java应用程序的完整生命周期运行
- **解决方案**: 切换到Agent模式，实现自动开始/结束

### 主要变化
- ✅ **CPU分析**: 从attach模式切换到agent模式
- ✅ **内存分析**: 保持attach模式（JFR输出需要）
- ✅ **生命周期管理**: Agent自动跟随应用程序
- ✅ **配置集中化**: 保持现有配置结构

## 🔧 技术实现详情

### 1. 配置区域更新

#### 新增配置变量
```bash
# --- 性能分析配置 (async-profiler v4.1 Agent模式) ---
ASYNC_PROFILER_LIB="${ASYNC_PROFILER_PATH}/lib/libasyncProfiler.so" # Agent库路径
PROFILING_FORMAT="html"                   # 默认输出格式
```

#### 移除的配置
```bash
# 不再需要
PROFILING_DURATION=0                      # Agent模式无需持续时间
```

### 2. Agent库检查

#### 新的检查逻辑
```bash
check_async_profiler() {
    # 检查agent库文件
    if [ ! -f "$ASYNC_PROFILER_LIB" ]; then
        log_error "Async Profiler Agent库不存在: $ASYNC_PROFILER_LIB"
        return 1
    fi
    
    # 检查库文件权限
    if [ ! -r "$ASYNC_PROFILER_LIB" ]; then
        log_error "Async Profiler Agent库无读取权限"
        return 1
    fi
}
```

### 3. JVM参数集成

#### Agent参数构建
```bash
# 构建agent参数字符串
if [ "$ENABLE_CPU_PROFILING" = "true" ]; then
    CPU_AGENT_PARAMS="start,event=${CPU_PROFILING_EVENT},interval=${PROFILING_INTERVAL},file=${CPU_FLAME_GRAPH_FILE}"
    AGENT_PARAMS="$CPU_AGENT_PARAMS"
fi

# 添加到JVM参数
if [ "$ENABLE_PROFILING" = "true" ] && [ "$ENABLE_CPU_PROFILING" = "true" ]; then
    JVM_ARGS+=(
        "-agentpath:${ASYNC_PROFILER_LIB}=${AGENT_PARAMS}"
    )
fi
```

### 4. 混合模式架构

#### CPU分析 (Agent模式)
- **启动方式**: JVM启动时自动加载
- **生命周期**: 跟随应用程序完整运行
- **输出格式**: HTML火焰图
- **优势**: 无时间限制，完整覆盖

#### 内存分析 (Attach模式)
- **启动方式**: 应用程序启动后attach
- **生命周期**: 独立进程管理
- **输出格式**: JFR文件
- **原因**: JFR输出需要attach模式

## 📊 模式对比

### Agent模式 vs Attach模式

| 特性 | Agent模式 | Attach模式 |
|------|-----------|------------|
| 启动时机 | JVM启动时 | 应用运行后 |
| 生命周期 | 跟随应用程序 | 独立进程 |
| 时间限制 | 无限制 | 默认10秒 |
| 配置复杂度 | 简单 | 复杂 |
| 进程管理 | 自动 | 手动 |
| 输出格式 | HTML | HTML/JFR |
| 性能开销 | 低 | 中等 |

### 使用场景

#### Agent模式适用于
- ✅ CPU性能分析
- ✅ 长时间运行的应用
- ✅ 需要完整生命周期覆盖
- ✅ 简化配置和管理

#### Attach模式适用于
- ✅ 内存分配分析
- ✅ JFR文件生成
- ✅ 运行时动态分析
- ✅ 多事件类型分析

## 🚀 使用方法

### 基本使用

#### 1. 启用CPU分析 (Agent模式)
```bash
ENABLE_PROFILING=true
ENABLE_CPU_PROFILING=true
ENABLE_ALLOC_PROFILING=false
```

#### 2. 启用混合分析
```bash
ENABLE_PROFILING=true
ENABLE_CPU_PROFILING=true      # Agent模式
ENABLE_ALLOC_PROFILING=true    # Attach模式
```

#### 3. 仅启用内存分析
```bash
ENABLE_PROFILING=true
ENABLE_CPU_PROFILING=false
ENABLE_ALLOC_PROFILING=true    # 仅Attach模式
```

### 配置自定义

#### Agent参数自定义
```bash
# 修改事件类型
CPU_PROFILING_EVENT="wall"     # 或 "cpu", "itimer"

# 修改采样间隔
PROFILING_INTERVAL="5ms"       # 更高精度

# 自定义输出文件
CPU_FLAME_GRAPH_FILE="custom_cpu_profile.html"
```

#### 高级配置
```bash
# 完整的agent参数示例
AGENT_PARAMS="start,event=cpu,interval=10ms,file=profile.html,threads,jfr"
```

## 🔍 验证和测试

### 验证Agent模式

#### 1. 检查JVM参数
```bash
# 查看Java进程的JVM参数
ps aux | grep java | grep agentpath
```

#### 2. 检查Agent库
```bash
# 验证库文件存在
ls -la /opt/async-profiler/lib/libasyncProfiler.so

# 检查权限
file /opt/async-profiler/lib/libasyncProfiler.so
```

#### 3. 监控文件生成
```bash
# 实时监控火焰图文件生成
watch -n 5 'ls -la cpu_flamegraph_*.html'
```

### 测试步骤

#### 1. 快速测试
```bash
# 使用小数据集测试
DATASET_PATH='data/airport.csv'
ALGORITHM_TIMEOUT="5"
./run_experiments.sh
```

#### 2. 验证输出
```bash
# 检查生成的文件
ls -la cpu_flamegraph_*.html
ls -la alloc_profiling_*.jfr
```

#### 3. 查看日志
```bash
# 检查Agent启动日志
grep -i "agent" gc-128gb-*.log
```

## 🛠️ 故障排除

### 常见问题

#### 1. Agent库不存在
**错误**: `Async Profiler Agent库不存在`
**解决**:
```bash
# 检查安装
ls -la /opt/async-profiler/lib/
# 重新安装async-profiler
sudo ./install_async_profiler.sh
```

#### 2. 权限问题
**错误**: `Agent库无读取权限`
**解决**:
```bash
sudo chmod 644 /opt/async-profiler/lib/libasyncProfiler.so
```

#### 3. JVM启动失败
**错误**: `Could not find agent library`
**解决**:
```bash
# 检查库路径
echo $ASYNC_PROFILER_LIB
# 验证库文件
file $ASYNC_PROFILER_LIB
```

#### 4. 火焰图未生成
**可能原因**:
- Agent参数配置错误
- 输出路径权限问题
- 应用程序运行时间过短

**解决**:
```bash
# 检查Agent参数
echo $AGENT_PARAMS
# 检查输出目录权限
ls -la $(dirname $CPU_FLAME_GRAPH_FILE)
```

### 调试技巧

#### 1. 启用详细日志
```bash
DEBUG_MODE=true ./run_experiments.sh
```

#### 2. 手动测试Agent
```bash
# 手动测试Agent加载
java -agentpath:/opt/async-profiler/lib/libasyncProfiler.so=start,event=cpu,file=test.html -version
```

#### 3. 检查进程状态
```bash
# 查看Java进程
ps aux | grep java
# 查看profiler进程
ps aux | grep asprof
```

## 📈 性能优化

### Agent模式优化

#### 1. 采样间隔调整
```bash
# 高精度分析
PROFILING_INTERVAL="1ms"

# 低开销分析
PROFILING_INTERVAL="100ms"
```

#### 2. 事件类型选择
```bash
# CPU时间分析
CPU_PROFILING_EVENT="cpu"

# 墙钟时间分析
CPU_PROFILING_EVENT="wall"
```

#### 3. 输出优化
```bash
# 压缩输出
AGENT_PARAMS="start,event=cpu,file=profile.html,collapsed"

# 包含线程信息
AGENT_PARAMS="start,event=cpu,file=profile.html,threads"
```

## 🎉 总结

### 迁移成果

#### 主要优势
- ✅ **无时间限制**: Agent模式跟随应用程序完整生命周期
- ✅ **简化管理**: 自动开始/结束，无需手动进程管理
- ✅ **完整覆盖**: 从应用启动到结束的完整性能数据
- ✅ **混合架构**: CPU用Agent，内存用Attach，各取所长

#### 保持的功能
- ✅ 配置集中化
- ✅ 内存验证
- ✅ PLI模式切换
- ✅ 错误处理
- ✅ 文件命名约定

#### 技术亮点
- 🚀 **智能模式选择**: 根据分析类型自动选择最佳模式
- 🚀 **混合架构**: Agent + Attach模式的完美结合
- 🚀 **向后兼容**: 保持所有现有功能
- 🚀 **用户友好**: 详细的状态信息和错误提示

现在您可以享受Agent模式带来的便利，同时保持所有原有功能的完整性！
